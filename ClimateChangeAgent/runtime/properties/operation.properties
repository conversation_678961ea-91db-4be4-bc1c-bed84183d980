#the agent's name and the client configuration are set in the file specified by operation.agentdefinition
operation.agentdefinition=agent.xml

# Set operation.noconditionui=true, plus the desired operation.room and operation.conditions parameters
#  to run without an initialization dialog for these parameters
operation.noconditionui=true

# Agent room name and optional conditions if these won't be specified by an initialization dialog
operation.room=ROOM
#operation.conditions=tutorial_trigger,social,participation,explain,say_more,explain_other,accountable_talk
#operation.conditions=tutorial_trigger,social,participation,explain,revoice,say_more,accountable_talk,explain_other
#operation.conditions=tutorial_trigger,social,participation,explain_other
#operation.conditions=tutorial_trigger,social,participation,explain_other,revoice,accountable_talk
#operation.conditions=tutorial_trigger,social,participation,agree,explain,revoice,say_more,accountable_talk,explain_other,reasoning
operation.conditions=tutorial_trigger,social,participation,explain_other,agree,say_more

operation.hasdebugui=true
operation.envlistener=client
operation.envactor=client

#these are the active preprocessors - they annotate messages 
#and provide new events for the 'listeners'==actors to react to.
#Give the full classpath for any preprocessors or listeners. The list is comma-separated. 
#"\" at the end of the line lets you wrap the list over multiple lines.
#Note that some components are both preprocessors and listeners -- list them once in each section.
#
#PresenceWatcher notices when students enter or leave, and fires a "LaunchEvent" once enough are present.
#MessageAnnotator adds annotations based on regular expressions or keywords to MessageEvents (used by several actors)
#
#TutorialTriggerWatcher notices opportunities to launch interactive dialogues.
#TutorTurnWatcher preprocesses student and tutor events for the TutorActor. 
#
#The social preprocessors and "computers" provide suggestions for the SocialController actor.
#The various accountable talk preprocessors detect facilitation candidates.
operation.preprocessors=basilica2.agents.listeners.PresenceWatcher,\
						basilica2.agents.listeners.MessageAnnotator,\
						basilica2.myagent.listeners.QuestionActor,\
						basilica2.myagent.listeners.SayMoreActor,\
						basilica2.myagent.listeners.AgreeDisagreeAnnotator,\
						basilica2.myagent.listeners.AskForExplanationAnnotator
#						basilica2.agents.listeners.EtherpadListener,\
#						basilica2.myagent.listeners.AgreeDisagreeAnnotator,\
#						basilica2.myagent.listeners.AgreeDisagreeActor,\
#						basilica2.myagent.listeners.RevoiceActor,\
#						basilica2.myagent.listeners.SayMoreActor,\				

#these are the active 'listeners'==actors. They propose actions that the agent might execute.
#
#Note that some components are both preprocessors and listeners -- list them once in each section.	
#SocialController enacts moves that promote social cohesion.
#PlanExecutor launches a static script in response to a LaunchEvent.
#
#The various accountable talk actors each implement a facilitation move
#  after a candidate student turn is detected, if the other criteria are met
#  see the individual properties files for each actor for more detail.	
operation.listeners=	basilica2.agents.listeners.plan.PlanExecutor,\
						basilica2.myagent.listeners.QuestionActor,\
						basilica2.myagent.listeners.SayMoreActor,\
						basilica2.myagent.listeners.AskForExplanationActor,\
						basilica2.myagent.listeners.AgreeDisagreeActor
#						basilica2.myagent.listeners.RevoiceActor,\
#						basilica2.myagent.listeners.SayMoreActor,\
