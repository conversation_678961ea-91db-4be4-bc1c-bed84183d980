#!/bin/bash

# ClimateChangeAgent Runner Script
# This script runs the ClimateChangeAgent from the command line

# Source the profile to ensure Java is available
source ~/.zprofile

# Set the working directory to the ClimateChangeAgent runtime directory
cd "$(dirname "$0")/runtime"

# Build the classpath based on the .classpath file
CLASSPATH="../bin"
CLASSPATH="$CLASSPATH:../../AccountableTalkAgent/bin"
CLASSPATH="$CLASSPATH:../../BaseAgent/bin"
CLASSPATH="$CLASSPATH:../../BasilicaCore/bin"
CLASSPATH="$CLASSPATH:../../SocialAgent/bin"
CLASSPATH="$CLASSPATH:../../SocketIOClient/bin"
CLASSPATH="$CLASSPATH:../../TuTalkSlim/bin"
CLASSPATH="$CLASSPATH:../../TutorAgent/bin"
CLASSPATH="$CLASSPATH:../../Genesis-Plugins/bin"
CLASSPATH="$CLASSPATH:../../Synonymizer/bin"
CLASSPATH="$CLASSPATH:../../LightSideMessageAnnotator/bin"

# Add JAR files
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/jopt/jopt-simple-4.3.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/commons-lang3-3.2.1.jar"
CLASSPATH="$CLASSPATH:../../TuTalkSlim/lib/commons-lang3-3.2.1.jar"
CLASSPATH="$CLASSPATH:../../TuTalkSlim/lib/lingpipe-2.3.0.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/socket.io-client-2.1.0.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/engine.io-client-2.1.0.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/json-org.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/okhttp-4.0.1.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/okio-2.3.0.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/kotlin-stdlib-1.3.41.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/kotlin-stdlib-common-1.3.41.jar"
CLASSPATH="$CLASSPATH:../../SocketIOClient/lib/jsoup-1.8.1.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/Moodle/mysql-connector-java-5.1.26-bin.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/agilo-client.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/agilo-common.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-chatblocks.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-framework.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-laf.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-mathexpression.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-spellcheck.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-utils.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/base-whiteboard.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-client.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-common.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-jdicbrowser.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-replication.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-screenshotchat.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-tabbedchat.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-user.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-virtualmeeting.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-vncviewer.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-webpagechat.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/cc-whiteboardchat.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/jazzy-core.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/jazzy-swing.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/jdic.jar"
CLASSPATH="$CLASSPATH:../../BaseAgent/lib/Environments/ConcertChat/Libraries/log4j.jar"
CLASSPATH="$CLASSPATH:../../BasilicaCore/lib/OtherLibraries/JGraph/jgraph.jar"
CLASSPATH="$CLASSPATH:../../BasilicaCore/lib/OtherLibraries/Utilities.jar"
CLASSPATH="$CLASSPATH:../../BasilicaCore/lib/OtherLibraries/Xerces/xml-apis.jar"
CLASSPATH="$CLASSPATH:../../BasilicaCore/lib/OtherLibraries/Xerces/xercesImpl.jar"

# Main class to run
MAIN_CLASS="basilica2.myagent.operation.NewAgentRunner"

# Default parameters
ROOM_NAME="ROOM"
LAUNCH_ARGS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --room)
            ROOM_NAME="$2"
            shift 2
            ;;
        --launch)
            LAUNCH_ARGS="--launch"
            shift
            ;;
        --help)
            echo "Usage: $0 [--room ROOM_NAME] [--launch] [--help]"
            echo "  --room ROOM_NAME    Set the room name (default: ROOM)"
            echo "  --launch           Launch without UI"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo "Starting ClimateChangeAgent..."
echo "Room: $ROOM_NAME"
echo "Working directory: $(pwd)"

# Run the agent
java -cp "$CLASSPATH" "$MAIN_CLASS" --room "$ROOM_NAME" $LAUNCH_ARGS
