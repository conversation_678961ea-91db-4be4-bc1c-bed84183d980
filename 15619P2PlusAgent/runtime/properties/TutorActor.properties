#Tutor<PERSON><PERSON> manages the behavior of tutorial dialogue scripts.
#These scripts can be triggered by the macro-script, or by the TutorialTriggerWatcher.

#this file describes the set of available tutorials, and their triggers.
dialogue_config_file=dialogues/dialogues-example.xml

#this is the folder where all the individual dialogue scripts live.
dialogue_folder=dialogues

#How long to wait before poking, if students don't respond
timeout1=120

#How long to wait after poking, before giving up or moving on.
timeout2=180

#If true, push ahead with the dialogue even when students don't respond.
start_anyways=true

#nudge given when students don't respond to the offer for starting a tutorial.
requestpokeprompt=I am waiting for your response to start. Please ask for help if you are stuck.

#message displayed after a timeout, before beginning the tutorial
#(when start_anyways is true) 
goaheadprompt=Okay, let's talk about this.

#nudge given when students don't respond during a tutorial dialogue.
#TO DO gst: revise
responsepokeprompt=Can you rephrase your response?

#message displayed if students say "I don't know"
dontknowprompt=Make an educated guess?

#message displayed after a timeout during a tutorial
#(when start_anyways is true) 
moveonprompt=Looks like it's time to move on.