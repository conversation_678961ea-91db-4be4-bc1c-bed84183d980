#this xml file describes the set of tutorial dialogues
#and also defines the trigger conditions for each.
dialogue_config_file=dialogues/dialogues-example.xml

#this matches to the "condition" checkboxes in the launch window, 
#as specified in operation.properties
#if the condition isn't set on launch, tutorials won't trigger
#(but can still be launched from macro-scripts - see TutorActor.properties)
trigger_condition=tutorial_trigger

# To limit starting tutorial dialogs to specific stages or steps, list the specific stage names or step names here
# -- If a stage name is listed, no need to list step names within that stage
# -- Do NOT include spaces between stage names or step names
no_dialogs_for_stagenames=initiate,stageA,stageB
no_dialogs_for_stepnames= 