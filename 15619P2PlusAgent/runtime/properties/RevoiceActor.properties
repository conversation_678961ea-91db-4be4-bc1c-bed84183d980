# GENERAL
# expert_statement_file contains examples of the kind of student statements that should trigger this kind of feedback.
# content_synonym_file contains sets of words that should be considered synonyms in this domain 
# - words in this set are special to the domain, and get extra weight when they match.
# synonym_file contains sets of words that are more generally synonymous
# accountable_prompt_file contains the set of prompts that are given as feedback when this behavior is triggered.
# - prompts will be selected that have the prompt_label tag, given above.
expert_statement_file=accountable/revoice_statements.txt
content_synonym_file=accountable/content_synonyms.txt
synonym_file=accountable/synonyms.txt
stopwords_file=accountable/stopwords.txt
accountable_prompt_file=accountable/accountable_prompts.xml
# REVOICE ACTOR
# When the Revoice Actor detects a student turn that might be a paraphrase 
#   of one of the exemplar statements, it offers the closest exemplar match as a "revoicing" of the student turn:
#   "So what I hear you saying is [STATEMENT]. Is that what you meant?"
# 
condition_flag=revoice
prompt_label=REVOICE
candidate_label=REVOICABLE

# TIMING
# candidate_window: the time (in seconds) after a candidate student turn is detected before the feedback is given.
#   This gives the students time to follow up on their own. 
#   If the discussion is productive, the agent does not need to intervene.
# 
# feedback_window: the time (in seconds) for which the agent waits 
#   (suppressing other agent behaviors) after this behavior is enacted.
#   This gives the students time to process and respond without interruption.
# 
# blackout_timeout: the additional time during which all other AT behaviors are supressed,
#   but not other agent behaviors (like social moves, or macro-scripts)
# 
# candidate_window=20
candidate_window=1
# feedback_window=45
feedback_window=5
# blackout_timeout=10
blackout_timeout=1

# FOLLOWUP
# productive_student_turns: a student turn is "productive" if it matches one of these annotations
#   if such a turn happens in the window following this behavior's triggering, the feedback is supressed.
# 
# productive_followup_turns: some behaviors (like agree-disagree) have a secondary behavior 
#   when a productive student turn is detected. This secondary followup is also supressed
#   if one of these annotations is detected.
# 
# Where do annotations come from? Either by matching a dictionary file in the dictionaries folder
#   (wordlists and regular expressions, like "CHALLENGE_CONTRIBUTION" and "QUESTION")
#   or they're added by other Bazaar listeners (like "AGREE_CANDIDATE" and "REVOICABLE")
# 
productive_student_turns=AGREE DISAGREE CHALLENGE_CONTRIBUTION QUESTION AGREE_CANDIDATE REVOICABLE
productive_followup_turns=EXPLANATION_CONTRIBUTION AGREE_CANDIDATE REVOICABLE

# OPPORTUNITIES
# candidate_check_priority: How important is this feedback? 
#   This is a relative priority value between 0 and 1.  Lower values may result 
#   in these moves being delayed or dropped in favor of other higher-priority actions.
# 
# target_ratio: How much of the target behavior do we want to see? 
#   This is somewhat mysterious, and is actor-dependent. Higher values mean the move will trigger more often.
#   For RevoiceActor, this is the ideal ratio of candidate statements out of total turns (for each students).
#   If the student's revoicable ratio is over this threshold, the move will be suppressed (they don't need our help).
# 
# skip_ratio: given a valid opportunity to perform this move, how often should the agent randomly decide not to do it?
# 
# minimum_match: how similar to an exemplar statement does a student turn have to be to trigger this behavior?
#   This number is particularly high for RevoiceActor, because the paraphrase really ought to be a close match.
#   It's lower for AgreeDisagreeActor, because the precise content of the match doesn't matter as much.
# 
# use_wordnet: use the WordNet dictionary to supplement the hand-crafted synonym lists. 
#   Recommended for general domains, but may cause inflated similarity values.
# 
# candidate_check_priority=0.8
candidate_check_priority=1.0
target_ratio=1.0
# target_ratio=0.5 
skip_ratio=1.0
# minimum_match=0.7
minimum_match=0.6
use_wordnet=true
