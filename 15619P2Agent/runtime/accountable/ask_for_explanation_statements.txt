agree
don't agree
believe
disagree
seems like
looks like
let's try
let's do
let's use
should
should not
shouldn't
must
may be
maybe
should use
can try
maybe we should
maybe we can
maybe try
try
i think we can
i think we should
i think our 
I guess
probably
preprocessor
fit_transform
transform
fit
predict
distribution
pattern
scale
scaled
scaler
scaling
StandardScaler
standard
approximate
approximating
shape
X_train
X_test
Y_train
Y_test
train
test
train data
test data
train set
test set
dataset
datasets
overfit
underfit
logisticregression
score
model
print
accuracy
mean accuracy
bias
biased
mean
var
variance
predict_one
cpu
gpu
cpu_predict
gpu_predict
join
batch
batches
large
small
batch size
overhead
split
splitting
compute
computing 
computation
computational
computationally
matrices
matric
parallel
parallelize
parallelism
process
processing
execute
execution
efficient
efficiently
independent
independently
sequence
sequential
sequentially
tradeoff
iterate
epoch
epochs
forward
backward
pass
input
inputs
output
outputs
net
syntax
loss
losses
grad
gradient
gradients
accumulate
accumulated
accumulating
accumulation
optimization
label
labels
prediction
predictions
logits
tensor
tensors
element
elements
remove
removing
primer