#this is the user that the <PERSON><PERSON><PERSON> agent will appear as in the chat.
#it must be an actual username in your moodle.
moodle_agent_username=robot

#check your moodle's config.php file for all of the mysql db values below.
db_user=moodle
db_pass=moodle

#change this to the hostname or IP address of the moodle mysql server,
#if you're running the agent on another machine.
db_host=localhost

#the port that mysql is running on.
#3306 and 8889 are likely values.
db_port=8889

#Moodle 2.5 uses moodle25, Moodle 2.6 uses moodle26 - check your config.php
db_name=moodle25

#Time in milliseconds between checking for new messages
check_interval=1000
