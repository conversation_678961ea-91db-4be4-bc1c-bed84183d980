<?xml version="1.0" encoding="UTF-8"?>
<plan name="15619 P2 OPE">
	<!-- "timeout" for a stage is an absolute number of seconds that elapses 
	(from the script's beginning) before that stage starts.  -->
	<!-- "delay" for a stage is the number seconds that elapses in-between stages.  -->
	
  
   	<!-- a step's "type" determines which <PERSON><PERSON><PERSON><PERSON> handles the step - see PlanExecutor.properties.-->
	<!-- "timeout" for a step is an upper-bound duraton for the step, after which next step starts.  -->
    <!-- "delay" for a step is the delay *after* the step completes on its own.-->
    <!-- <stage name="StageInitialization" type="other" delay="0">  -->
     	<!-- <step type="greet" timeout="45" delay="0">GREETINGS</step> --> 
    	<!-- <step type="prompt" delay="4" prompt="PROMPT_GREETING">PROMPT_GREETING</step>   -->
    	<!-- <step type="prompt" delay="1" prompt="PROMPT_SESSION_TIME">PROMPT_GREETING</step> -->
    <!-- </stage>   -->s
    
    <stage name="StageLO1" type="other" delay="0">
    	<step type="prompt" delay="4" prompt="PROMPT_GREETING">PROMPT_GREETING</step>   
    	<step type="send_command" delay="5" command="COMMAND_1">COMMAND_1</step>  
    	<step type="prompt" delay="5" prompt="PROMPT_SESSION_TIME">PROMPT_SESSION_TIME</step>  
    	<step type="prompt" delay="5" prompt="PROMPT_START_TASK1">PROMPT_START_TASK1</step>
    	<step type="send_external_message" delay="5" message="task=task1-start">START_1</step> 
        <step type="match" delay="5" prompt="PROMPT_STEP_MATCH" >Prompt_Step_LO1_Match</step> 
        <step type="file_gated" delay="5" timeout="2281" file="testcase-complete_1" delayed_prompt_time="1320" delayed_prompt="BOTTOM-OUT_TASK-1" checkin_prompt="NONE" warning_prompt="NONE">Test_case_1</step>	<!-- timeout="1680"; delayed_prompt_time="1380" --> 
    </stage>   
     
    <stage name="StageLO1_Discussion" type="other" delay="0">    	  	
        <step type="prompt" delay="5" prompt="PROMPT_MOVE_ON_1">PROMPT_MOVE_ON_1</step>  
    	<step type="send_external_message" delay="30" message="task=task1-complete-a">COMPLETE_1A</step>  
    	<step type="send_external_message" delay="30" message="task=task1-complete-b">COMPLETE_1B</step>  
    	<step type="send_external_message" delay="18" message="task=task1-complete-c">COMPLETE_1C</step> 
    </stage>  
    
    <stage name="StageLO2" type="other" delay="0">  
    	<step type="send_external_message" delay="5" message="task=task2-start">MESSAGE_2</step>
    	<step type="send_command" delay="5" command="COMMAND_2">COMMAND_2</step>     
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK2">Prompt_Role_Switch_Task2</step>  
        <step type="rotate" delay="5" prompt="PROMPT_STEP_ROTATE_FANCY" >Prompt_Step_LO2_Rotate</step> 
        <step type="file_gated" delay="5" timeout="1682" file="testcase-complete_2" delayed_prompt_time="960" delayed_prompt="BOTTOM-OUT_TASK-2" checkin_prompt="NONE" warning_prompt="NONE">Test_case_2</step>	<!-- timeout="1320"; delayed_prompt_time="1020" -->  
    </stage> 
     
    <stage name="StageL02_Discussion" type="other" delay="0">      	  	
        <step type="prompt" delay="5" prompt="PROMPT_MOVE_ON_2">PROMPT_MOVE_ON_2</step>    
    	<step type="send_external_message" delay="30" message="task=task2-complete-a">COMPLETE_2A</step>  
    	<step type="send_external_message" delay="30" message="task=task2-complete-b">COMPLETE_2B</step>  
    	<step type="send_external_message" delay="18" message="task=task2-complete-c">COMPLETE_2C</step> 
    </stage>   
    
    <stage name="StageLO3" type="other" delay="0">  
    	<step type="send_external_message" delay="5" message="task=task3-start">MESSAGE_3</step>
    	<step type="send_command" delay="5" command="COMMAND_3">COMMAND_3</step> 
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK3">Prompt_Role_Switch_Task3</step>  
        <step type="rotate" delay="5" prompt="PROMPT_STEP_ROTATE_PLAIN" >Prompt_Step_LO3_Rotate</step> 
        <step type="file_gated" delay="5" timeout="1682" file="testcase-complete_3" delayed_prompt_time="960" delayed_prompt="BOTTOM-OUT_TASK-3" checkin_prompt="NONE" warning_prompt="NONE">Test_case_3</step>	<!-- timeout="1320"; delayed_prompt_time="1020" -->      
    </stage>  
     
    <stage name="StageLO3_Discussion" type="other" delay="0">         	  	
        <step type="prompt" delay="5" prompt="PROMPT_MOVE_ON_3">PROMPT_MOVE_ON_3</step>        
    	<step type="send_external_message" delay="30" message="task=task3-complete-a">COMPLETE_3A</step>  
    	<step type="send_external_message" delay="30" message="task=task3-complete-b">COMPLETE_3B</step>  
    	<step type="send_external_message" delay="20" message="task=task3-complete-c">COMPLETE_3C</step> 
    </stage>  
   
    <stage name="WrapUp" type="other" delay="0">    
    	<step type="send_external_message" delay="5" message="task=taskall-end">MESSAGE_END</step>	    
    	<step type="send_command" command="COMMAND_SUBMIT" delay="5">COMMAND_SUBMIT</step>    
        <step type="prompt" delay="10" prompt="PROMPT_STEP_SUBMISSION" >Prompt_Step_Submission</step>
    	<step type="send_end" delay="5">send_end</step>  
    </stage> 
  
    <stage name="EndStage" type="other" delay="0" timeout="6060">  
    	<step type="send_external_message" delay="5" message="task=taskall-end">MESSAGE_END</step>	    
    	<step type="send_command" command="COMMAND_SUBMIT" delay="5">COMMAND_SUBMIT</step>  
        <step type="prompt" delay="10" prompt="PROMPT_STEP_FINAL" >Prompt_Step_Final</step>
        <step type="chatlog" delay="10" >CHAT_LOGS</step> 
    	<step type="send_end" delay="5">send_end</step>  
        <step type="logout" delay="0">LOGOUT</step>
    </stage>
    
</plan>