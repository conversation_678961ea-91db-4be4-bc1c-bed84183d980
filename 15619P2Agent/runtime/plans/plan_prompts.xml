<?xml version="1.0" encoding="UTF-8"?>
<prompts>


	<!-- GREETING PROMPTS -->  
	
    <prompt strategy="task" id="PROMPT_INTRODUCE">
        <text>We're starting! I'm OPE_Bot.|||Tell the team your name and share something about yourself, such as a hobby you have or a game you like to play. Ask questions about what the others share. Take a moment to learn more about each other.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_GREET">
        <text>Hi, [NAME].</text>
        <text>Nice to meet you, [NAME].</text>
        <text>Howdy, [NAME].</text>
        <text>Hello, [NAME].</text>
    </prompt>
	
	<prompt strategy="task" id="PROMPT_GREETING">
     	<text>We're starting! I'm OPE_Bot.</text>
    </prompt> 



	<!-- GENERAL PROMPTS --> 
    
	<prompt strategy="task" id="PROMPT_MYSQL_REMINDER">
     	<text>Remember that you can login to MySQL as follows.
-- Command: mysql -u root -pCloudCC@100 -h 127.0.0.1
-- Within MySQL, use database 'employees'.</text>
    </prompt> 
 


	<!-- HINTS & DISCUSSION FOR INITIALIZATION -->

	<prompt strategy="task" id="PROMPT_SESSION_TIME">
     	<text>Beginning now, you will have approximately 100 minutes to complete the OPE tasks and submit to receive your grades.</text>
    </prompt> 
	
	<prompt strategy="task" id="PROMPT_PRIMERS">
     	<text>Please share with the team (and me) which primer(s) you read for this session.</text>
    </prompt> 

 


	<!-- HINTS & DISCUSSION FOR TASK 1 -->
	<!-- GATED PROMPTS are in gatekeeper_prompts.xml -->>  
	
	<prompt strategy="task" id="PROMPT_PRE-TASK_DISCUSSION_1">
     	<text>In preparation for the first task, who can share with the team when it is appropriate to apply denormalization within a database? This was discussed in the Normalization and Denormalization primer.</text>
    </prompt>  
      
    <prompt strategy="task" id="PROMPT_START_TASK1">
        <text>Okay! Let's assign your responsibilities for the first task.</text>
    </prompt>  
      
    <prompt strategy="task" id="PROMPT_TASK1_REMINDER">
        <text>Please connect to MySQL by following the instructions in the workspace, then continue with the instructions for Task 1.</text>
    </prompt>  
    
    <prompt strategy="task" id="PROMPT_TEST_QUERY_1">
        <text>To submit your final query for Task 1, copy it to the Task 1 cell within double quotes, then run the Testing Task 1 cell.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_FEEDBACK_LOCATION_1">
        <text>You will be able to see feedback for each final query attempt in the output of the Testing Task 1 cell.</text>
    </prompt>
	
	<prompt strategy="task" id="PROMPT_DISCUSSION_START_1">
     	<text>IT'S TIME TO MOVE ON FROM TASK 1. But first take turns responding to the following question.</text>
    </prompt> 
	
	<prompt strategy="task" id="PROMPT_MOVE_ON_1">
     	<text>IT'S TIME TO MOVE ON FROM TASK 1.</text>
    </prompt> 
    
    <prompt strategy="task" id="PROMPT_STEP_LO1_REFLECTION_1">
     	<text>Exactly how can creating a denormalized table improve efficiency?</text>
    </prompt>
     
    <prompt strategy="task" id="PROMPT_STEP_LO1_REFLECTION_2" intention="cut_off_PROMPT_STEP_LO1_REFLECTION_2">
     	<text>Give some examples of how changing a datatype can decrease space usage, query time, or both.</text>
    </prompt>



	<!-- HINTS & DISCUSSION FOR TASK 2 -->
	<!-- GATED PROMPTS are in gatekeeper_prompts.xml --> 
	
	<prompt strategy="task" id="PROMPT_PRE-TASK_DISCUSSION_2">
     	<text>In preparation for Task 2, who can share with the team the key considerations for creating effective composite indexes in a database? This was discussed in the Indexing primer.</text>
    </prompt>  
    
    <prompt strategy="task" id="PROMPT_ROLE_SWITCH_TASK2">
        <text>IT'S TIME TO START TASK 2. Read and follow the instructions in the Task 2 cell.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_TEST_QUERY_2">
        <text>To submit your final query for Task 2, copy it to the Task 2 cell within double quotes, then run the Testing Task 2 cell.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_FEEDBACK_LOCATION_2">
        <text>You will be able to see feedback for each final query attempt in the output of the Testing Task 2 cell.</text>
    </prompt> 
	
	<prompt strategy="task" id="PROMPT_DISCUSSION_START_2">
     	<text>IT'S TIME TO MOVE ON FROM TASK 2. But first take turns responding to the following question.</text>
    </prompt> 
	
	<prompt strategy="task" id="PROMPT_MOVE_ON_2">
     	<text>IT'S TIME TO MOVE ON FROM TASK 2.</text>
    </prompt> 
     
    <prompt strategy="task" id="PROMPT_STEP_LO2_REFLECTION_1">
     	<text>Exactly how can creating an index improve query speed?</text>
    </prompt>
     
    <prompt strategy="task" id="PROMPT_STEP_LO2_REFLECTION_2">
     	<text>Exactly how can creating an index improve query speed?</text>
    </prompt>



	<!-- HINTS & DISCUSSION FOR TASK 3 -->
	<!-- GATED PROMPTS are in gatekeeper_prompts.xml -->
	
	<prompt strategy="task" id="PROMPT_PRE-TASK_DISCUSSION_3">
     	<text>In preparation for the last task, who can share with the team when it is more suitable to use INT rather than CHAR in a database? This was discussed in the Data Types primer.</text>
    </prompt> 
      
    <prompt strategy="task" id="PROMPT_ROLE_SWITCH_TASK3">
        <text>IT'S TIME TO START THE LAST TASK. Read and follow the instructions in the Task 3 cell.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_TEST_QUERY_3">
        <text>To submit your final query for Task 3, copy it to the Task 3 cell within double quotes, then run the Testing Task 3 cell.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_FEEDBACK_LOCATION_3">
        <text>You will be able to see feedback for each final query attempt in the output of the Testing Task 3 cell.</text>
    </prompt> 
	
	<prompt strategy="task" id="PROMPT_DISCUSSION_START_3">
     	<text>IT'S TIME TO MOVE ON FROM THE LAST TASK. But first take turns responding to the following question.</text>
    </prompt> 
	
	<prompt strategy="task" id="PROMPT_MOVE_ON_3">
     	<text>IT'S TIME TO MOVE ON FROM TASK 3.</text>
    </prompt> 
     
    <prompt strategy="task" id="PROMPT_STEP_LO3_REFLECTION_1">
     	<text>What are some tradeoffs that you considered between the three optimization methods -- for example between indexing and denormalization?</text>
    </prompt>
     
    <prompt strategy="task" id="PROMPT_STEP_LO3_REFLECTION_2">
     	<text>What are the advantages to using a composite index and what are some of its limitations?</text>
    </prompt>



	<!-- WRAP-UP AND END-STAGE PROMPTS --> 
     
    <prompt strategy="task" id="PROMPT_STEP_SUBMISSION_PREVIOUS">
     <text>Thanks for participating in today's OPE activity.|||Next, ONE TEAM MEMBER should execute the submit cell. After submission, your post-quiz token will be displayed below the cell. Copy this token and navigate to the "Post OPE Quiz" section of this module on Sail() to unlock and complete your post-quiz.</text>
    </prompt>
     
    <prompt strategy="task" id="PROMPT_STEP_SUBMISSION">
     <text>The time allotted for each of the three tasks has passed. However, if there is more you would like to do, you may keep working until 100 minutes have passed, at which point I will tell you I am logging off.|||When you are ready, ONE TEAM MEMBER should execute the submit cell. After submission, your post-quiz token will be displayed below the cell. Copy this token and navigate to the "Post OPE Quiz" section of this module on Sail() to unlock and complete your post-quiz.|||Thanks for participating in today's OPE.</text>
    </prompt>
     
    <prompt strategy="task" id="PROMPT_STEP_FINAL">
     	<text>I'm logging off. Thanks for participating in today's OPE activity.|||Next, ONE TEAM MEMBER should execute the submit cell. After submission, your post-quiz token will be displayed below the cell. Copy this token and navigate to the "Post OPE Quiz" section of this module on Sail() to unlock and complete your post-quiz.</text>
    </prompt>


 
	<!-- ASSIGNMENT PROMPTS --> 
    
    <prompt strategy="task" id="PROMPT_STEP_MATCH">
        <text>[NAME1], you will be the Driver for this task since no one else joined in time. While you are by yourself, try to follow the responsibilities associated with all the roles -- i.e. try to first brainstorm for ideas, then analyze their pros and cons to choose one, and then implement it.|||If someone joins late, have them assume one of the missing roles and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_MATCH_1">
        <text>[NAME1], you will be the Driver for this task since no one else joined in time. While you are by yourself, try to follow the responsibilities associated with all the roles -- i.e. try to first brainstorm for ideas, then analyze their pros and cons to choose one, and then implement it.|||If someone joins late, have them assume one of the missing roles and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_MATCH_2">
        <text>Your initial roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
Recall that the navigator brainstorms ideas and decides on one which the driver then implements. If someone joins late, have them assume the researcher role and assist until you pass a test case. The project manager role is unassigned because this OPE is designed for teams of three.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_MATCH_3">
        <text>Your initial roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
[ROLE3] - [NAME3]
Recall that the researcher refers to resources like the primer as necessary. The project manager role is unassigned because this OPE is designed for teams of three.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_MATCH_MAX_ALL">
        <text>Your initial roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
[ROLE3] - [NAME3]
Whoever doesn't have an assignment is a researcher for this round. Recall that the researcher refers to resources like the primer as necessary. The project manager role is unassigned because this OPE is designed for teams of three.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_FANCY">
        <text>[NAME1], you will be the Driver for this task since no one else joined in time. While you are by yourself, try to follow the responsibilities associated with all the roles -- i.e. try to first brainstorm for ideas, then analyze their pros and cons to choose one, and then implement it.|||If someone joins late, have them assume one of the missing roles and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_FANCY_1">
        <text>[NAME1], you will be the Driver for this task since no one else joined in time. While you are by yourself, try to follow the responsibilities associated with all the roles -- i.e. try to first brainstorm for ideas, then analyze their pros and cons to choose one, and then implement it.|||If someone joins late, have them assume one of the missing roles and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_FANCY_2">
        <text>Your new roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
Recall that the navigator brainstorms ideas and decides on one which the driver then implements. If someone joins late, have them assume the researcher role and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_FANCY_3">
        <text>Your new roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
[ROLE3] - [NAME3]
Recall that the researcher refers to resources like the primer as necessary.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_FANCY_MAX_ALL">
        <text>Your new roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
[ROLE3] - [NAME3]
Whoever doesn't have an assignment is a researcher for this round. Recall that the researcher refers to resources like the primer as necessary.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_PLAIN">
        <text>[NAME1], you will be the Driver for this task since no one else joined in time. While you are by yourself, try to follow the responsibilities associated with all the roles -- i.e. try to first brainstorm for ideas, then analyze their pros and cons to choose one, and then implement it.|||If someone joins late, have them assume one of the missing roles and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_PLAIN_1">
        <text>[NAME1], you will be the Driver for this task since no one else joined in time. While you are by yourself, try to follow the responsibilities associated with all the roles -- i.e. try to first brainstorm for ideas, then analyze their pros and cons to choose one, and then implement it.|||If someone joins late, have them assume one of the missing roles and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_PLAIN_2">
        <text>Your new roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
If someone joins late, have them assume the researcher role and assist until you pass a test case.</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_PLAIN_3">
        <text>Your new roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
[ROLE3] - [NAME3]</text>
    </prompt>
    
    <prompt strategy="task" id="PROMPT_STEP_ROTATE_PLAIN_MAX_ALL">
        <text>Your new roles are -
[ROLE1] - [NAME1]
[ROLE2] - [NAME2]
[ROLE3] - [NAME3]
Whoever doesn't have an assignment is a researcher for this round.</text>
    </prompt>


</prompts>