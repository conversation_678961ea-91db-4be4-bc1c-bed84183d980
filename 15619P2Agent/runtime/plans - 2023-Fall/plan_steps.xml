<?xml version="1.0" encoding="UTF-8"?>
<plan name="15619 P2 OPE">
	<!-- "timeout" for a stage is an absolute number of seconds that elapses 
	(from the script's beginning) before that stage starts.  -->
	<!-- "delay" for a stage is the number seconds that elapses in-between stages.  -->
	
  
   	<!-- a step's "type" determines which <PERSON><PERSON><PERSON><PERSON> handles the step - see PlanExecutor.properties.-->
	<!-- "timeout" for a step is an upper-bound duraton for the step, after which next step starts.  -->
    <!-- "delay" for a step is the delay *after* the step completes on its own.-->
    <stage name="StageInitialization" type="other" delay="0"> 
     	<!-- <step type="greet" timeout="45" delay="0">GREETINGS</step> --> 
    	<step type="prompt" delay="5" prompt="PROMPT_GREETING">PROMPT_GREETING</step>  
    	<step type="prompt" delay="5" prompt="PROMPT_SESSION_TIME">PROMPT_GREETING</step>
    	<step type="prompt" delay="3" prompt="PROMPT_PRIMERS">PROMPT_PRIMERS</step>
    </stage>  
    
    <stage name="StageLO1" type="other" delay="0">
    	<step type="logstate" delay="3" state_tag="agent" state_value="15619P2" send_log="false">logstate_agent-name</step> 
    	<step type="logstate" delay="3" state_tag="state" state_value="Programming Phase of OPE" send_log="false">logstate_programming-phase</step> 
    	<step type="logstate" delay="3" state_tag="phase_duration" state_value="80">logstate_phase-duration-1</step> 
    	<step type="logstate" delay="3" state_tag="ope_iteration" state_value="1">logstate_ope-iteration-1</step>    	  	
        <step type="prompt" delay="75" prompt="PROMPT_PRE-TASK_DISCUSSION_1">PROMPT_PRE-TASK_DISCUSSION_1</step> 
    	<step type="send_command" delay="5" command="COMMAND_1">COMMAND_1</step>    
    	<step type="prompt" delay="5" prompt="PROMPT_START_TASK1">PROMPT_START_TASK1</step>
        <step type="match" delay="5" prompt="PROMPT_STEP_MATCH" >Prompt_Step_LO1_Match</step> 
    	<step type="prompt" delay="5" prompt="PROMPT_TASK1_REMINDER">PROMPT_TASK1_REMINDER</step>  
    	<step type="send_external_message" delay="5" message="task=task1">MESSAGE_1</step> 
        <step type="file_gated" delay="5" timeout="1620" file="testcase-complete_1" delayed_prompt_time="1320" delayed_prompt="BOTTOM-OUT_TASK-1" checkin_prompt="NONE" warning_prompt="NONE">Test_case_1</step>	<!-- timeout="1680"; delayed_prompt_time="1380" --> 
    </stage>   
     
    <stage name="StageLO1_Discussion" type="other" delay="0">    	  	
        <step type="prompt" delay="10" prompt="PROMPT_DISCUSSION_START_1">PROMPT_DISCUSSION_START_1</step>    	
        <step type="prompt" delay="60" prompt="PROMPT_STEP_LO1_REFLECTION_1">PROMPT_STEP_LO1_REFLECTION_1</step>  	
        <!-- <step type="prompt" delay="75" prompt="PROMPT_STEP_LO1_REFLECTION_2">PROMPT_STEP_LO1_REFLECTION_2</step> -->
    </stage>  <!-- 10,75,75 -->
    
    <stage name="StageLO2" type="other" delay="0">  
    	<step type="logstate" delay="5" state_tag="ope_iteration" state_value="2">logstate_ope-iteration-2</step>     	  	
        <step type="prompt" delay="75" prompt="PROMPT_PRE-TASK_DISCUSSION_2">PROMPT_PRE-TASK_DISCUSSION_2</step>
    	<step type="send_external_message" delay="5" message="task=task2">MESSAGE_2</step>
    	<step type="send_command" delay="5" command="COMMAND_2">COMMAND_2</step>     
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK2">Prompt_Role_Switch_Task2</step>  
        <step type="rotate" delay="10" prompt="PROMPT_STEP_ROTATE_FANCY" >Prompt_Step_LO2_Rotate</step> 
        <step type="file_gated" delay="5" timeout="1260" file="testcase-complete_2" delayed_prompt_time="960" delayed_prompt="BOTTOM-OUT_TASK-2" checkin_prompt="NONE" warning_prompt="NONE">Test_case_2</step>	<!-- timeout="1320"; delayed_prompt_time="1020" -->  
    </stage> 
     
    <stage name="StageL02_Discussion" type="other" delay="0">           	      	    	       	      	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_START_2">PROMPT_DISCUSSION_START_2</step>    	
        <step type="prompt" delay="60" prompt="PROMPT_STEP_LO2_REFLECTION_1">PROMPT_STEP_LO2_REFLECTION_1</step>
        <!-- <step type="prompt" delay="75" prompt="PROMPT_STEP_LO2_REFLECTION_2">PROMPT_STEP_LO2_REFLECTION_2</step>  -->
    </stage>   <!-- 5,75,75 -->
    
    <stage name="StageLO3" type="other" delay="0">  
    	<step type="logstate" delay="5" state_tag="ope_iteration" state_value="3">logstate_ope-iteration-3</step>     	  	
        <step type="prompt" delay="75" prompt="PROMPT_PRE-TASK_DISCUSSION_3">PROMPT_PRE-TASK_DISCUSSION_3</step>
    	<step type="send_external_message" delay="5" message="task=task3">MESSAGE_3</step>
    	<step type="send_command" delay="5" command="COMMAND_3">COMMAND_3</step> 
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK3">Prompt_Role_Switch_Task3</step>  
        <step type="rotate" delay="10" prompt="PROMPT_STEP_ROTATE_PLAIN" >Prompt_Step_LO3_Rotate</step> 
        <step type="file_gated" delay="5" timeout="1260" file="testcase-complete_3" delayed_prompt_time="960" delayed_prompt="BOTTOM-OUT_TASK-3" checkin_prompt="NONE" warning_prompt="NONE">Test_case_3</step>	<!-- timeout="1320"; delayed_prompt_time="1020" -->      
    </stage>  
     
    <stage name="StageLO3_Discussion" type="other" delay="0">     	      	     	     	      	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_START_3">PROMPT_DISCUSSION_START_3</step>    	
        <step type="prompt" delay="70" prompt="PROMPT_STEP_LO3_REFLECTION_1">PROMPT_STEP_LO3_REFLECTION_1</step>
        <!-- <step type="prompt" delay="75" prompt="PROMPT_STEP_LO3_REFLECTION_2">PROMPT_STEP_LO3_REFLECTION_2</step> -->
    </stage>   <!-- 5,75,75 -->
   
    <stage name="WrapUp" type="other" delay="0">    
    	<step type="send_external_message" delay="5" message="task=end">MESSAGE_END</step>	    
    	<step type="send_command" command="COMMAND_SUBMIT" delay="5">COMMAND_SUBMIT</step>    
        <step type="prompt" delay="10" prompt="PROMPT_STEP_SUBMISSION" >Prompt_Step_Submission</step>
    	<step type="logstate" delay="5" state_tag="state" state_value="OPE ITERS END" log_tag="OPE END">log_state</step>   
    	<step type="send_end" delay="5">send_end</step>  
    </stage> 
  
    <stage name="EndStage" type="other" delay="0" timeout="4920">     
    	<step type="send_external_message" delay="5" message="task=end">MESSAGE_END</step>	    
    	<step type="send_command" command="COMMAND_SUBMIT" delay="5">COMMAND_SUBMIT</step>  
        <step type="prompt" delay="10" prompt="PROMPT_STEP_FINAL" >Prompt_Step_Final</step>
    	<step type="logstate" delay="5" state_tag="state" state_value="OPE ITERS END" log_tag="OPE END">log_state</step>  
        <step type="chatlog" delay="10" >CHAT_LOGS</step> 
    	<step type="send_end" delay="5">send_end</step>  
        <step type="logout" delay="0">LOGOUT</step>
    </stage>
    
</plan>