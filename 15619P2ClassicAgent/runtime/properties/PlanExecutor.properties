#PlanExecutor launches the given macro-script in response to the first LaunchEvent
#(triggered by the PresenceWatcher)

#the default delay between plan steps.
planexecutor.interstepseconds=5

#this file contains the structure of the script.
planexecutor.plan_file=plans/plan_steps.xml

#don't worry about this.
planexecutor.statememory=stateMemory

#StepHandlers handle specific types of plan steps.
#"listen" includes all the AT moves and social support.
#"prompt" is the generic "say something" type,
#   looking up its prompt value from the plan_prompts file.
#"gated" steps wrap another step given in the "gated_type" property.
#the "logout" step disconnects the agent from the chat.
planexecutor.step_handlers=prompt:basilica2.agents.listeners.plan.PromptStepHandler,\
						   match:basilica2.agents.listeners.plan.MatchStepHandler,\
						   rotate:basilica2.agents.listeners.plan.RotateStepHandler,\
						   greet:basilica2.agents.listeners.plan.GreetStepHandler,\
						   sendlog:basilica2.agents.listeners.plan.LogStepHandler,\
						   send_end:basilica2.agents.listeners.plan.EndStepHandler,\
						   logstate:basilica2.agents.listeners.plan.LogStateStepHandler,\
						   send_command:basilica2.agents.listeners.plan.SendCommandStepHandler,\
						   file_gated:basilica2.agents.listeners.plan.FileStepHandler,\
						   chatlog:basilica2.agents.listeners.plan.ChatLogHandler,\
						   logout:basilica2.agents.listeners.plan.LogoutStepHandler
#						   