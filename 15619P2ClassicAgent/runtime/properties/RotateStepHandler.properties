#Prompts in the macro-script are drawn from this file.
prompt_file=plans/plan_prompts.xml

#defaultPromptPriority=1.0
#rate_limited=false
#delay_after_prompt=false

roles=Driver,Navigator,Researcher
default_role=Researcher

# When num_users >= min_users_to_match, users will be assigned roles, otherwise there is no role assignment at the start of the stage.
# match_multiple_to_default=True means when num_users > num_roles, every user will be assigned a role. 
# match_multiple_to_default=False means when num_users > num_roles, only some of the users are assigned roles and the rest have no role. 
min_users_to_match=1
max_users_to_match=3
match_multiple_to_default=true

# Whether to send role match results to be logged remotely. Currently used with remote MobBot for Cloud Computing.
send_match_remote_log=true

#reading speed for controlling automatic post-prompt delay.
words_per_minute=400