# === TO RUN SOCKETS VERSION THAT WORKS WITH DOCKER, SocketIOClient ===
# socket_url=http://0.0.0.0:80
socket_url=http://127.0.0.1
#socket_url=http://bazaar.lti.cs.cmu.edu:8300
socket_suburl=/bazsocket/socket.io


# === TO RUN LEGACY SOCKETS VERSION, SocketIOClientLegacy ===
#change this to the hostname or IP address of the socket.io server,
#if you're running the agent on another machine.
#socket_url=http://bazaar.lti.cs.cmu.edu:80
#socket_url=http://conversation.lti.cs.cmu.edu:8000
