<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE scenario SYSTEM "TuTalkScenario.dtd">
<scenario default-language="en" scenario-name="scenario-cf_temperature">
	<configuration>
		
		<!-- It *is* possible to hook up a LightSide classifier to TuTalk, 
		such that the predicted labels are treated as concepts. 
		but not today. -->
			
		<!-- 	
		<module kind="model" name="animals"
			classname="edu.cmu.cs.lti.tutalk.module.SidePredictor">
			<param key="path" value="models/animals.ser" />
		</module> 
		-->

	</configuration>

	<!-- I'm not entirely sure what these are used for. -->
	<transitions>
		<transition ack-type="agree" floor-status="neutral">
			<tphrase> yes </tphrase>
			<tphrase> okay </tphrase>
		</transition>
	
		<transition ack-type="none">
			<tphrase> moving on... </tphrase>
		</transition>
	</transitions>
	
	<!-- 'Concepts' define things that students might say,
	     AND things that the tutor might say back. 
	     Tutor concepts should take the form of phrase lists.
		 Student concepts can be phrase lists, 
		 regular expressions, or lists of annotations,
	     but only one of these at a time. -->
		
	<concepts>
		<!-- this is a special kind of concept for catching unexpected student responses -->
		<concept label="unanticipated-response">
			<phrase>anything else</phrase>
		</concept>
		
		
		<!-- tutor concepts. -->
		<concept label="introduction">
			<phrase>Let's see how much you know about cold fronts and temperature.
            </phrase>
		</concept>

		<concept label="conclusion">
			<phrase>Let's move on to other question.</phrase>
		</concept>

		<concept label="conclusion1">
			<phrase>Let's move on to other topic.</phrase>
		</concept>

		<concept label="question1">
			<phrase>Where is the temperature cooler in regards to a Cold front?
            </phrase>
		</concept>

		<concept label="question2">
			<phrase>When warm and cold fronts meet, there is likely to be a Low Pressure center. That means the temperature at these intersections is… ?
            </phrase>
		</concept>


		<concept label="unrecognized">
			<phrase>I don't know what that is.</phrase>
			<phrase>That doesn't make sense to me.</phrase>
		</concept>
		
		<concept label="correct">
			<phrase>Well done!</phrase>
			<phrase>Okeydoke.</phrase>
			<phrase>Sounds good...</phrase>
		</concept>
		
		<concept label="ok">
			<phrase>Ok</phrase>
		</concept>


		<concept label="response1_1">
			<phrase>That is correct. The temperature is cooler behind a cold front.</phrase>
		</concept>

		<concept label="response1_2">
			<phrase>That is incorrect.</phrase>
		</concept>

		<concept label="response1_2_1">
			<phrase>That is correct. The temperature is cooler behind a cold front.</phrase>
		</concept>

		<concept label="response1_2_2">
			<phrase>That is incorrect.</phrase>
		</concept>

		<concept label="response2_1">
			<phrase>That is correct. Since cold air sinks and low pressure centers are concentrations of air low in the atmosphere, the temperature is most likely cooler.</phrase>
		</concept>

		<concept label="response2_2">
			<phrase>That is incorrect.</phrase>
		</concept>

		<concept label="response2_2_1">
			<phrase>That is correct. Since cold air sinks and low pressure centers are concentrations of air low in the atmosphere, the temperature is most likely cooler.</phrase>
		</concept>

		<concept label="response2_2_2">
			<phrase>That is incorrect.</phrase>
		</concept>

	


		<concept label="correct_response1">
			<phrase>That is incorrect. The temperature cooler behind a cold front.
			</phrase>
		</concept>

		<concept label="correct_response2">
			<phrase>That is incorrect. Since cold air sinks and low pressure centers are concentrations of air low in the atmosphere, the temperature is most likely cooler.
			</phrase>
		</concept>


		<concept label="yes" type="annotation">
			<phrase>AFFIRMATIVE</phrase>
		</concept>
		
		<concept label="no" type="annotation">
			<phrase>NEGATIVE</phrase>
		</concept>


		<concept label="answer1_1" type="regex">
			<phrase>behind|back|backward|in the back</phrase>
		</concept>

		<concept label="answer1_2" type="regex">
			<phrase>front|forward|in front</phrase>
		</concept>

		<concept label="answer1_2_1" type="regex">
			<phrase>behind|back|backward|in the back</phrase>
		</concept>

		<concept label="answer1_2_2" type="regex">
			<phrase>front|forward|in front</phrase>
		</concept>

		<concept label="answer2_1" type="regex">
			<phrase>cooler|cool|low|lower</phrase>
		</concept>
		
	    <concept label="answer2_2" type="regex">
			<phrase>warm|warmer|high|higher</phrase>
		</concept>

		 <concept label="answer2_2_1" type="regex">
			<phrase>cooler|cool|low|lower</phrase>
		</concept>

		 <concept label="answer2_2_2" type="regex">
			<phrase>warm|warmer|high|higher</phrase>
		</concept>

		

	</concepts>


	<script>
		<goal name="start" difficulty="1">
			
			<step>
				<!-- deliver a statement from the concept named "introduction"  -->
				<initiation>introduction</initiation>
			</step>
			
			<step>
				<!-- Every step begins with an initiating concept or literal phrase -->
				<initiation>question1</initiation>
				
				<!-- These are the "response" options. 
					If the concept between the tags matches the student statement, 
				    the "say" concept/phrase will be delivered by the tutor.
				    "push" adds another goal to the dialogue's stack. 
					 Note that goals should NOT be recursively nested. -->
					
				<response say="response1_1">answer1_1</response>
				<response push="elicitate1_1" say="response1_2">answer1_2</response>
				<response push="elicitate1_2" say="response1_2">unanticipated-response</response>
			</step>

			<step>
				<initiation>conclusion</initiation>
			</step>

			
			<step>
				<!-- Every step begins with an initiating concept or literal phrase -->
				<initiation>question2</initiation>
				
				<!-- These are the "response" options. 
					If the concept between the tags matches the student statement, 
				    the "say" concept/phrase will be delivered by the tutor.
				    "push" adds another goal to the dialogue's stack. 
					 Note that goals should NOT be recursively nested. -->
					
				<response say="response2_1">answer2_1</response>
				<response push="elicitate2_1" say="response2_2">answer2_2</response>
				<response push="elicitate2_2" say="response2_2">unanticipated-response</response>
			</step>

			<step>
				<initiation>conclusion1</initiation>
			</step>
			
		</goal>


		<goal name="elicitate1_1" difficulty="0">

			<step>
				<initiation>Remember that temperature fronts indicate the temperature behind the front. Where is the temperature cooler in regards to a cold front?</initiation>
                <response say="response1_2_1">answer1_2_1</response>
				<response say="correct_response1">answer1_2_2</response>
				<response say="correct_response1">unanticipated-response</response>
			</step>
		</goal>

		<goal name="elicitate1_2" difficulty="0">

			<step>
				<initiation>I did not understand your answer. Where is the temperature cooler in regards to a Cold front?</initiation>
                <response say="response1_2_1">answer1_2_1</response>
				<response say="correct_response1">answer1_2_2</response>
				<response say="correct_response1">unanticipated-response</response>
			</step>
		</goal>

		<goal name="elicitate2_1" difficulty="0">

			<step>
				<initiation>Low pressure centers represent air concentrated lower in the atmosphere. That means the temperature of this air is most likely...?</initiation>
                <response say="response2_2_1">answer2_2_1</response>
				<response say="correct_response2">answer2_2_2</response>
				<response say="correct_response2">unanticipated-response</response>
			</step>
		</goal>

		<goal name="elicitate2_2" difficulty="0">

			<step>
				<initiation>I did not understand your answer. When warm and cold fronts meet, there is likely to be a Low Pressure center. That means the temperature at these intersections is… ?</initiation>
                <response say="response2_2_1">answer2_2_1</response>
				<response say="correct_response2">answer2_2_2</response>
				<response say="correct_response2">unanticipated-response</response>
			</step>
		</goal>

		
	</script>

</scenario>