<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE scenario SYSTEM "TuTalkScenario.dtd">
<scenario default-language="en" scenario-name="scenario-lp_precipitation">
	<configuration>
		
		<!-- It *is* possible to hook up a LightSide classifier to TuTalk, 
		such that the predicted labels are treated as concepts. 
		but not today. -->
			
		<!-- 	
		<module kind="model" name="animals"
			classname="edu.cmu.cs.lti.tutalk.module.SidePredictor">
			<param key="path" value="models/animals.ser" />
		</module> 
		-->

	</configuration>

	<!-- I'm not entirely sure what these are used for. -->
	<transitions>
		<transition ack-type="agree" floor-status="neutral">
			<tphrase> yes </tphrase>
			<tphrase> okay </tphrase>
		</transition>
	
		<transition ack-type="none">
			<tphrase> moving on... </tphrase>
		</transition>
	</transitions>
	
	<!-- 'Concepts' define things that students might say,
	     AND things that the tutor might say back. 
	     Tutor concepts should take the form of phrase lists.
		 Student concepts can be phrase lists, 
		 regular expressions, or lists of annotations,
	     but only one of these at a time. -->
		
	<concepts>
		<!-- this is a special kind of concept for catching unexpected student responses -->
		<concept label="unanticipated-response">
			<phrase>anything else</phrase>
		</concept>
		
		
		<!-- tutor concepts. -->
		<concept label="introduction">
			<phrase>We can talk about low pressure and precipitation in a little more detail.
            </phrase>
		</concept>
	
		<concept label="conclusion1">
			<phrase>Please feel free to continue discussing among yourselves.</phrase>
		</concept>
					
		<concept label="question1">
			<phrase>Can you explain why the chance of precipitation is most likely higher at a low pressure center?
            </phrase>
		</concept>
		
		<!-- the tutor will choose randomly between multiple phrases for a concept. -->
		
		<concept label="unrecognized">
			<phrase>I don't know what that is.</phrase>
			<phrase>That doesn't make sense to me.</phrase>
		</concept>
		
		<concept label="correct">
			<phrase>Well done!</phrase>
			<phrase>Okeydoke.</phrase>
			<phrase>Sounds good...</phrase>
		</concept>
		
		<concept label="ok">
			<phrase>Ok</phrase>
		</concept>

		
		<concept label="response1_1">
			<phrase>It sounds like you're on the right track. In a low pressure center, the air is pushed upwards and condenses, so there is more moisture and clouds. This increases the chance of precipitation.</phrase>
		</concept>

		<concept label="response1_2">
			<phrase>It sounds like you could use some help.
			</phrase>
		</concept>

		<concept label="response1_2_1">
			<phrase>It sounds like you're on the right track. In a low pressure center, the air is pushed upwards and condenses, so there is more moisture and clouds. This increases the chance of precipitation.</phrase>
		</concept>

		<concept label="response1_2_2">
			<phrase>It sounds like you could use some help.
			</phrase>
		</concept>

		
		<concept label="correct_response1">
			<phrase>Let me help you out. In a low pressure center, the air is pushed upwards and condenses, so there is more moisture and clouds. This increases the chance of precipitation.
			</phrase>
		</concept>
	
		<!-- end tutor concepts. -->
		
		
		<!-- student concepts. -->
		
		<!-- these match against external (MessageAnnotator) annotations. -->
		<concept label="yes" type="annotation">
			<phrase>AFFIRMATIVE</phrase>
		</concept>
		
		<concept label="no" type="annotation">
			<phrase>NEGATIVE</phrase>
		</concept>
		
		<!-- these match against regular expressions. -->

		<concept label="answer1_1" type="regex">
			<phrase>air pushed up|air pushed upward|air pushed high|air pushed higher|pushed up|pushed upward|condenses|air condenses|gets heavier|more dense|more moisture|more clouds
			</phrase>
		</concept>
		
	    <concept label="answer1_2" type="regex">
			<phrase>air pushed down|air pushed downward|air pushed low|air pushed lower|pushed down|pushed downward|expands|air expands|gets lighter|less dense|less moisture|less clouds
			</phrase>
		</concept>

		<concept label="answer1_2_1" type="regex">
			<phrase>condenses|air condenses|gets heavier|more dense|more moisture|more clouds
			</phrase>
		</concept>
		
	    <concept label="answer1_2_2" type="regex">
			<phrase>air pushed down|air pushed downward|air pushed low|air pushed lower|pushed down|pushed downward|expands|air expands|gets lighter|less dense|less moisture|less clouds
			</phrase>
		</concept>		

	</concepts>
	
	
	<script>
		<goal name="start" difficulty="1">
			
			<step>
				<!-- deliver a statement from the concept named "introduction"  -->
				<initiation>introduction</initiation>
			</step>
			
			<step>
				<!-- Every step begins with an initiating concept or literal phrase -->
				<initiation>question1</initiation>
				
				<!-- These are the "response" options. 
					If the concept between the tags matches the student statement, 
				    the "say" concept/phrase will be delivered by the tutor.
				    "push" adds another goal to the dialogue's stack. 
					 Note that goals should NOT be recursively nested. -->
					
				<response say="response1_1">answer1_1</response>
				<response push="elicitate1_1" say="response1_2">answer1_2</response>
				<response push="elicitate1_2" say="response1_2">unanticipated-response</response>
			</step>
			
			<step>
				<initiation>conclusion1</initiation>
			</step>
			
		</goal>
		

		<goal name="elicitate1_1" difficulty="0">

			<step>
				<initiation>In a low pressure center, the air is pushed upwards. Why do you think the chance of precipitation is higher at a low pressure center?</initiation>
                <response say="response1_2_1">answer1_2_1</response>
				<response say="correct_response1">answer1_2_2</response>
				<response say="correct_response1">unanticipated-response</response>
			</step>
		</goal>

		<goal name="elicitate1_2" difficulty="0">

			<step>
				<initiation>Sorry, I didn't understand you. In a low pressure center, the air is pushed upwards. Why do you think the chance of precipitation is higher at a low pressure center?</initiation>
                <response say="response1_2_1">answer1_2_1</response>
				<response say="correct_response1">answer1_2_2</response>
				<response say="correct_response1">unanticipated-response</response>
			</step>
		</goal>

	</script>

</scenario>
