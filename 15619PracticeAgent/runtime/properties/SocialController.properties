#The social controller responds to social triggers.
#The text of the social responses is defined by this file.
prompt_file=plans/social_prompts.xml

#The priority of social prompts - typically low, to allow other agent behavior precedence.
priority=1.0

#How long a social prompt will stay queued before it is discarded - typically short.
window=30

#How long to block other agent moves after successfully delivering a social move
blackout=5

#condition strings - these match up to the conditions in operation.properties 
#if these conditions are not active when the agent is launched, the agent won't
#prompt for social or participation support, respectively
social_condition=social
participation_condition=participation