<?xml version="1.0" encoding="UTF-8"?>
<plan name="15619 PRACTICE OPE">
	<!-- "timeout" for a stage is an absolute number of seconds that elapses 
	(from the script's beginning) before that stage starts.  -->
	<!-- "delay" for a stage is the number seconds that elapses in-between stages.  -->
   	<!-- a step's "type" determines which <PERSON><PERSON><PERSON><PERSON> handles the step - see PlanExecutor.properties.-->
	<!-- "timeout" for a step is an upper-bound duraton for the step, after which next step starts.  -->
    <!-- "delay" for a step is the delay *after* the step completes on its own.-->
    <stage name="StageInitialization" type="other" delay="0"> 
     	<step type="greet" timeout="45" delay="0">GREETINGS</step> 
    </stage>  
    
    <stage name="StageLO1" type="other" delay="0">
    	<step type="logstate" state_tag="state" state_value="Programming Phase of OPE" send_log="false" delay="2">log_state</step> 
    	<step type="logstate" state_tag="phase_duration" state_value="80" delay="2">log_state</step> 
    	<step type="logstate" state_tag="ope_iteration" state_value="1" delay="5">log_state</step>  
    	<step type="send_command" delay="4" command="COMMAND_1">COMMAND_1</step>  
    	<step type="prompt" delay="5" prompt="PROMPT_PROGRAMMNG_PHASE">Programming_Phase</step> 
        <step type="match" delay="5" prompt="PROMPT_STEP_MATCH" >Prompt_Step_LO1_Match</step>        
	<step type="file_gated" timeout="4800" delay="5" file="testcase-complete_1" delayed_prompt_time="900" delayed_prompt="PROMPT_STEP_LO1_SOLUTION" checkin_prompt="NONE" warning_prompt="NONE">Wait_Step_Task1</step>    <!-- 900 --> 
    </stage> 
     
    <stage name="StageLO1_Discussion" type="other" delay="0">    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_START1">Prompt_Step_LO1_Discussion_Start</step>    	  	       	
        <step type="prompt" delay="10" prompt="PROMPT_DISCUSSION_CREDIT">Prompt_Step_LO1_Discussion_credit</step>    	
        <step type="prompt" delay="60" prompt="PROMPT_STEP_LO1_REFLECTION">Prompt_Step_LO1_Reflection</step>    	<!-- 60 -->
    </stage> 
    
    <stage name="StageLO2" type="other" delay="0">  
    	<step type="logstate" state_tag="ope_iteration" state_value="2" delay="5">log_state</step>    
    	<step type="send_command" command="COMMAND_2" delay="5">COMMAND_2</step>   
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK2">Role_Switch_Warning</step> 
        <step type="rotate" delay="5" prompt="PROMPT_STEP_ROTATE_FANCY" >Prompt_Step_LO2_Rotate</step>      
        <step type="file_gated" timeout="4800" delay="5" file="testcase-complete_2" delayed_prompt_time="780" delayed_prompt="PROMPT_STEP_LO2_SOLUTION" checkin_prompt="NONE" warning_prompt="NONE">Wait_Step_Task2</step>   	 <!-- 780 -->
    </stage> 
     
    <stage name="StageLO2_Discussion" type="other" delay="0">    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_START2">Prompt_Step_LO2_Discussion_Start</step>    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_CREDIT">Prompt_Step_LO2_Discussion_credit</step>    	
        <step type="prompt" delay="60" prompt="PROMPT_STEP_LO2_REFLECTION">Prompt_Step_LO2_Reflection</step>    	<!-- 60 -->
    </stage> 
    
    <stage name="StageLO3" type="other" delay="0">
    	<step type="logstate" state_tag="ope_iteration" state_value="3" delay="5">log_state</step>    
    	<step type="send_command" command="COMMAND_3" delay="5">COMMAND_3</step>  
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK3">Role_Switch_Warning</step> 
        <step type="rotate" delay="5" prompt="PROMPT_STEP_ROTATE_PLAIN" >Prompt_Step_LO3_Rotate</step>  
        <step type="file_gated" timeout="4800" delay="5" file="testcase-complete_3" delayed_prompt_time="780" delayed_prompt="PROMPT_STEP_LO3_SOLUTION" checkin_prompt="NONE" warning_prompt="NONE">Wait_Step_Task3</step>     <!-- 780 -->
    </stage> 
     
    <stage name="StageLO3_Discussion" type="other" delay="0">    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_START3">Prompt_Step_LO3_Discussion_Start</step>    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_CREDIT">Prompt_Step_LO3_Discussion_credit</step>    	
        <step type="prompt" delay="60" prompt="PROMPT_STEP_LO3_REFLECTION">Prompt_Step_LO3_Reflection</step>    	<!-- 60 -->
    </stage> 
    
    <stage name="StageLO4" type="other" delay="0">    
    	<step type="logstate" state_tag="ope_iteration" state_value="4" delay="5">log_state</step>   
    	<step type="send_command" command="COMMAND_4" delay="5">COMMAND_4</step>  
    	<step type="prompt" delay="5" prompt="PROMPT_ROLE_SWITCH_TASK4">Role_Switch_Warning</step> 
        <step type="rotate" delay="5" prompt="PROMPT_STEP_ROTATE_FANCY" >Prompt_Step_LO4_Rotate</step> 
        <step type="file_gated" timeout="4800" delay="5" file="testcase-complete_4" delayed_prompt_time="780" delayed_prompt="PROMPT_STEP_LO4_SOLUTION" checkin_prompt="NONE" warning_prompt="NONE">Wait_Step_Task4</step>  <!-- 780 -->
    </stage> 
     
    <stage name="StageLO4_Discussion" type="other" delay="0">    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_START4">Prompt_Step_LO4_Discussion_Start</step>    	  	       	
        <step type="prompt" delay="5" prompt="PROMPT_DISCUSSION_CREDIT">Prompt_Step_LO4_Discussion_credit</step>    	
        <step type="prompt" delay="60" prompt="PROMPT_STEP_LO4_REFLECTION">Prompt_Step_LO4_Reflection</step>    	<!-- 60 -->
    </stage> 
    
    <stage name="WrapUP" type="other" delay="0">
    	<step type="send_command" command="COMMAND_SUBMIT" delay="5">COMMAND_SUBMIT</step>  
        <step type="prompt" delay="2" prompt="PROMPT_STEP_SUBMISSION" >Prompt_Step_Submission</step>
    	<step type="logstate" state_tag="state" state_value="OPE ITERS END" log_tag="OPE END" delay="2">log_state</step>   
    	<step type="send_end" delay="2">send_end</step>  
    </stage> 
  
    <stage name="EndStage" type="other" delay="0" timeout="4800">  
    	<step type="send_command" command="COMMAND_SUBMIT" delay="5">COMMAND_SUBMIT</step>  
        <step type="prompt" delay="5" prompt="PROMPT_STEP_FINAL" >Prompt_Step_Final</step>
    	<step type="logstate" state_tag="state" state_value="OPE ITERS END" log_tag="OPE END" delay="2">log_state</step>  
        <step type="chatlog" delay="2" >CHAT_LOGS</step> 
    	<step type="send_end" delay="2">send_end</step>  
        <step type="logout" delay="0">LOGOUT</step>
    </stage>
    
</plan>
