<?xml version="1.0" encoding="UTF-8"?>
<prompts>

	<prompt strategy="task" id="FILE_STEP_TIMEOUT_WARNING">
		<text>We need to start wrapping up.</text>
		<text>Okay, let's get ready to move on to reflection.</text>
	</prompt>

	
	<prompt strategy="task" id="FILE_STEP_TIMED_OUT">
		<text>We need to be moving along on this test case.</text>
		<text>Time for this test case is almost over.</text>
		<text>Time to move along on this test case if you can.</text>
	</prompt>
    
    <prompt strategy="task" id="FILE_STEP_COMPLETE" intention="test_case_passed">
       <text>You've passed the testcase!</text>
       <text>Great! You have passed the testcase.</text>
       <text>Way to go! You've passed the testcase.</text>
       <text>Good job. You've passed the testcase.</text>
    </prompt>
	
    <prompt strategy="task" id="WAIT_FOR_CHECKIN">
       <text>Okay, we're about to begin.</text>
    </prompt>
    
    <prompt strategy="task" id="TIMEOUT_WARNING">
       <text>We need to start wrapping up. Remember to type "ready" when you're ready to move on.</text>
       <text>Okay, let's get ready to move on. Remember to type "ready" if you're done with this step.</text>
    </prompt>
     
    <prompt strategy="task" id="PROMPT_STEP_LO1_SOLUTION" intention="bottom_out_hint_0">
     	<text>You should be moving along on this test case. Here's a hint in case you are stuck ...|||Did you explore the Pandas.read_csv() method?</text>
    </prompt>

     <prompt strategy="task" id="PROMPT_STEP_LO2_SOLUTION" intention="bottom_out_hint_1">
     	<text>You should be moving along on this test case. Here's a hint in case you are stuck ...|||Did you explore the groupby() method for the dataframe?</text>
     </prompt>
     
     <prompt strategy="task" id="PROMPT_STEP_LO3_SOLUTION" intention="bottom_out_hint_2">
     	<text>You should be moving along on this test case. Here's a hint in case you are stuck ...|||Did you explore the sum() method for the dataframe? </text>
     </prompt>
     
     <prompt strategy="task" id="PROMPT_STEP_LO4_SOLUTION" intention="bottom_out_hint_3">
     	<text>You should be moving along on this test case. Here's a hint in case you are stuck ...|||Did you explore the max() method for the dataframe? </text>
     </prompt>

    <!-- for READY_GENERIC -->    
    <prompt strategy="task" id="READY_GENERIC">
       <text>Type "ready" when you are ready to move on.</text>
    </prompt>
     
    <prompt strategy="task" id="READY_GENERIC_NO_READY">
       <text>We will move on after a while or when everyone is ready.</text>
    </prompt>
         
    <prompt strategy="task" id="WAIT_FOR_CONSENSUS">
       <text>When you've reached consensus in your group, type "ready".</text>
       <text>Make sure your partner agrees before continuing.</text>
       <text>We'll move to the next part once everyone is in agreement.</text>
    </prompt>
    
    <prompt strategy="task" id="ACKNOWLEDGE">
       <text>Thanks, [STUDENT]. Make sure your team is in agreement.</text>
       <text>Okay, [STUDENT]. Make sure your team agrees.</text>
       <text>Hang on until your team is ready, [STUDENT].</text>
       <text>Thanks, [STUDENT]. Hang on until your team is ready...</text>
    </prompt>
    
    <prompt strategy="task" id="ALL_READY">
       <text>Okay, let's move on...</text>
       <text>Moving on...</text>
       <text>Onward!</text>
    </prompt>
  
  
 <!-- ================================ -->
 <!-- The following prompts are unused -->
 <!-- ================================ -->
      
    <prompt strategy="task" id="WAIT_FOR_DISCUSSION">
       <text>We'll move on when everyone is ready.</text>
    </prompt>
       
    <prompt strategy="task" id="WAIT_FOR_READING">
       <text>Once you've read this and discussed it with your team, type "ready".</text>
       <text>When everyone is comfortable with this material, type "ready".</text>
    </prompt>
    
</prompts>

