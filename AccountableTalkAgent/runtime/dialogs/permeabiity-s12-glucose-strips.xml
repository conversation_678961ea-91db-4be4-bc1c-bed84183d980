<?xml version="1.0" encoding="UTF-8"?>
<scenario default-language="en">

<configuration>
<module name="nlu">
   <dict>
     <key>matchers</key><string>default taghelper</string>
     <key>matcher-policy</key><string>thresh</string>
     <key>taghelper-host</key><string>ankara.lti.cs.cmu.edu</string>
     <key>taghelper-port</key><string>7557</string>
     <key>taghelper-timeout</key><string>60</string>
   </dict>
</module>
</configuration>

<transitions>
  <transition ack-type="agree" floor-status="neutral">
    <tphrase> yes </tphrase>
    <tphrase> okay </tphrase>
  </transition>
  <transition ack-type="agree" scope="nonimmediate">
    <tphrase> Yes, I agree. </tphrase>
    <tphrase> Very good! </tphrase>
    <tphrase> Excellent! </tphrase>
  </transition>
  <transition ack-type="agree" ack-polarity="neg">
    <tphrase> I disagree with you. </tphrase>
    <tphrase> That doesn't sound right to me.</tphrase>
  </transition>
  <transition ack-type="understand" floor-status="neutral">
    <tphrase> I understand what you are saying. </tphrase>
    <tphrase> I think I understand what you mean. </tphrase>
  </transition>
  <transition ack-type="hear"  floor-status="concede" scope="immediate">   
    <tphrase> uh-huh </tphrase>
    <tphrase> hmmm-huh </tphrase>
    <tphrase> yeah </tphrase>
    <tphrase> I'm listening. </tphrase>
    <tphrase> Go on.</tphrase>
  </transition>
  <transition topic-status="refresh">
    <tphrase> So, back to the original question. </tphrase>
    <tphrase> Let's try the original question again. </tphrase>
    <tphrase> Once again on the original question. </tphrase>
   </transition>
   <transition topic-status="interrupt">
    <tphrase>
     Let's put this aside for a minute and come back to it later.
    </tphrase>
  </transition>
</transitions>

<concepts>
    <concept label="unanticipated-response">
        <phrase>anything else</phrase>
    </concept>
    <concept label="increase">
        <phrase>increase</phrase>
        <phrase>high</phrase>
        <phrase>up</phrase>
        <phrase>more</phrase>
        <phrase>higher</phrase>
    </concept>
    <concept label="decrease">
        <phrase>decrease</phrase>
        <phrase>less</phrase>
        <phrase>down</phrase>
        <phrase>low</phrase>
        <phrase>lower</phrase>
        <phrase>reduce</phrase>
    </concept>
    <concept label="yes">
        <phrase>yes</phrase>
        <phrase>why not</phrase>
        <phrase>sure</phrase>
        <phrase>yeah</phrase>
        <phrase>ok</phrase>
        <phrase>absolutely</phrase>
        <phrase>ofcourse</phrase>
        <phrase>of course</phrase>
        <phrase>yup</phrase>
        <phrase>i think so</phrase>
        <phrase>Right</phrase>
        <phrase>yeh</phrase>
        <phrase>yeahh</phrase>
        <phrase>yeahhh</phrase>
        <phrase>yesss</phrase>
    </concept>
    <concept label="no">
        <phrase>no</phrase>
        <phrase>dont think so</phrase>
        <phrase>never</phrase>
        <phrase>nope</phrase>
        <phrase>nah</phrase>
        <phrase>nay</phrase>
        <phrase>not really</phrase>
        <phrase>nahh</phrase>
    </concept>
    <concept label="materials">
        <phrase>break</phrase>
        <phrase>fail</phrase>
        <phrase>material</phrase>
        <phrase>strength</phrase>
        <phrase>properties</phrase>
        <phrase>property</phrase>
        <phrase>steel</phrase>
    </concept>
    <concept label="better">
        <phrase>good</phrase>
        <phrase>better</phrase>
        <phrase>improve</phrase>
    </concept>
    <concept label="worse">
        <phrase>poor</phrase>
        <phrase>degrade</phrase>
        <phrase>worse</phrase>
        <phrase>bad</phrase>
    </concept>
    <concept label="concept_262">
        <phrase>You can see the thermal energies for different fuels on Page 25 of the book. Which fuel is most suitable for high cycle efficiency?</phrase>
    </concept>
    <concept label="concept_362">
        <phrase>Right. Nuclear energy has the highest thermal energy.</phrase>
    </concept>
    <concept label="concept_363">
        <phrase>Nuclear energy has the highest thermal energy. </phrase>
    </concept>
    <concept label="concept_364">
        <phrase>Right. Power generated by the cycle increases as shown in graph 17</phrase>
    </concept>
    <concept label="concept_365">
        <phrase>Power generated by the cycle increases by using a higher thermal energy fuel.</phrase>
    </concept>
    <concept label="concept_366">
        <phrase>As more heat is added to the cycle due to the higher thermal energy of nuclear fuel, more work is done by the steam in the turbine.</phrase>
    </concept>
    <concept label="concept_367">
        <phrase>The heat rejected by the cycle increases by using a higher thermal energy input!</phrase>
    </concept>
    <concept label="concept_368">
        <phrase>Correct. The waste heat increases too.</phrase>
    </concept>
    <concept label="concept_369">
        <phrase>you are right.</phrase>
    </concept>
    <concept label="bestfuel">
        <phrase>nuclear</phrase>
        <phrase>nucelar</phrase>
        <phrase>atomic</phrase>
        <phrase>fission</phrase>
    </concept>
    <concept label="concept_371">
        <phrase>Graph 14 shows that cycle efficiency increases by using a fuel with high thermal energy.</phrase>
    </concept>
    <concept label="concept_372">
        <phrase>By using a fuel with higher thermal energy, does the power output increase or decrease?</phrase>
    </concept>
    <concept label="concept_373">
        <phrase>Can you explain why power generated increases by using a fuel like Nuclear energy?</phrase>
    </concept>
    <concept label="reason4">
        <phrase>more heat added</phrase>
        <phrase>more work done by steam in turbine</phrase>
    </concept>
    <concept label="concept_375">
        <phrase>On the other hand, what happens to heat rejected by the cycle by using a higher thermal energy input?</phrase>
    </concept>
    <concept label="concept_376">
        <phrase>We see this in Graph 16 of the worksheet.</phrase>
    </concept>
    <concept label="concept_377">
        <phrase>Now that you have seen how different choices of fuel affect the cycle parameters, you can discuss which fuel might help you to achieve your design objectives.</phrase>
    </concept>
</concepts>

<script>

<goal name="start" template-name="template_start_1">
  <step>
    <subgoal>goal_1</subgoal>
  </step>
</goal>

<goal name="goal_1" template-name="template_22" difficulty="1">
  <step>
    <initiation>concept_262</initiation>
    <response say="concept_362">bestfuel</response>
    <response say="concept_363">unanticipated-response</response>
  </step>
  <step>
    <initiation>concept_371</initiation>
  </step>
  <step>
    <initiation>concept_372</initiation>
    <response say="concept_364">better</response>
    <response say="concept_364">increase</response>
    <response say="concept_365">decrease</response>
    <response say="concept_365">worse</response>
    <response say="concept_365">unanticipated-response</response>
  </step>
  <step>
    <initiation>concept_373</initiation>
    <response say="concept_369">reason4</response>
    <response say="concept_366">unanticipated-response</response>
  </step>
  <step>
    <initiation>concept_375</initiation>
    <response say="concept_368">increase</response>
    <response say="concept_367">decrease</response>
    <response say="concept_367">unanticipated-response</response>
  </step>
  <step>
    <initiation>concept_376</initiation>
  </step>
  <step>
    <initiation>concept_377</initiation>
  </step>
</goal>


</script>
</scenario>
