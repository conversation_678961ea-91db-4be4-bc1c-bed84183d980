agilo.client.laf.className=de.fhg.ipsi.plaf.ConcertLookAndFeel
agilo.client.laf.configURL=http://localhost:1354/clients/resources/laf.xml
agilo.client.properties=http://localhost:9000/concertChat/agiloclientconfig/client.xml
agilo.connection.gcInterval=0
agilo.connection.http.connectURL=/concertChat/connect
agilo.connection.http.messageURL=/concertChat/messages
agilo.connection.http.pollInterval=250
agilo.connection.http.reconnectWait=2000
agilo.connection.inBufferSize=32768
agilo.connection.ioSleepTime=30
agilo.connection.marshaller=de.fraunhofer.ipsi.agilo.common.marshalling.DefaultMarshaller
agilo.connection.outBufferSize=32768
agilo.connection.ping.sendInterval=3000
agilo.connection.ping.timeout=6000
agilo.connection.reconnectWait=2000
agilo.connection.type=HTTP
agilo.logging.log4jConfig=http://localhost:9000/concertChat/agiloclientconfig/log4j.xml
agilo.logging.useThrowingLogger=false
agilo.server.name=localhost
agilo.server.port=5222
agilo.webserver.name=localhost
agilo.webserver.port=9000
ipsi.chat.test=false
ipsi.chat.test.delay=1000
ipsi.chat.test.msgcount=50
ipsi.chat.test.whbdelay=1000
ipsi.concertchat.actionshow=true
ipsi.concertchat.menuconf=http://localhost:9000/concertChat/agiloclientconfig/menuconf.xml
ipsi.loadBundleFromServer=true
application=de.fhg.ipsi.concertchat.applications.whiteboard.WhiteboardChat
