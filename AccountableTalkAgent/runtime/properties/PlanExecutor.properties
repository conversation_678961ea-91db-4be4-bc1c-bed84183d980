planexecutor.statememory=stateMemory
planexecutor.interstepseconds=10
planexecutor.plan_file=plans/basic_listener_steps.xml
planexecutor.plan_prompts=plans/bio_prompts_GD.xml
planexecutor.step_handlers=prompt:basilica2.agents.listeners.plan.PromptStepHandler,\
						   greet:basilica2.agents.listeners.plan.GreetStepHandler,\
						   whiteboard:basilica2.agents.listeners.plan.WhiteboardStepHandler,\
						   listen:basilica2.accountable.listeners.ConditionalListenStepHandler,\
						   process:basilica2.agents.listeners.plan.ProcessStepHandler,\
						   chatlog:basilica2.agents.listeners.plan.ChatLogHandler,\
						   listen:basilica2.bio.listeners.ListenStepHandler