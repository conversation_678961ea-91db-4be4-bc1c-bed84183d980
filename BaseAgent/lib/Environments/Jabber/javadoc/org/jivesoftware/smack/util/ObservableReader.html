<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.6.0_20) on Mon Jul 04 15:12:25 CDT 2011 -->
<TITLE>
ObservableReader (Smack 3.2.1 Documentation)
</TITLE>

<META NAME="date" CONTENT="2011-07-04">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="ObservableReader (Smack 3.2.1 Documentation)";
    }
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">
<HR>


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/DNSUtil.HostAddress.html" title="class in org.jivesoftware.smack.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/ObservableWriter.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/ObservableReader.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ObservableReader.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.io.Reader">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.jivesoftware.smack.util</FONT>
<BR>
Class ObservableReader</H2>
<PRE>
<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</A>
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">java.io.Reader</A>
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.jivesoftware.smack.util.ObservableReader</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Readable.html?is-external=true" title="class or interface in java.lang">Readable</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ObservableReader</B><DT>extends <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></DL>
</PRE>

<P>
An ObservableReader is a wrapper on a Reader that notifies to its listeners when
 reading character streams.
<P>

<P>
<DL>
<DT><B>Author:</B></DT>
  <DD>Gaston Dombiak</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.Reader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#lock" title="class or interface in java.io">lock</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#ObservableReader(java.io.Reader)">ObservableReader</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A>&nbsp;wrappedReader)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#addReaderListener(org.jivesoftware.smack.util.ReaderListener)">addReaderListener</A></B>(<A HREF="../../../../org/jivesoftware/smack/util/ReaderListener.html" title="interface in org.jivesoftware.smack.util">ReaderListener</A>&nbsp;readerListener)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a reader listener to this reader that will be notified when
 new strings are read.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#mark(int)">mark</A></B>(int&nbsp;readAheadLimit)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#markSupported()">markSupported</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#read()">read</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#read(char[])">read</A></B>(char[]&nbsp;cbuf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#read(char[], int, int)">read</A></B>(char[]&nbsp;cbuf,
     int&nbsp;off,
     int&nbsp;len)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#ready()">ready</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#removeReaderListener(org.jivesoftware.smack.util.ReaderListener)">removeReaderListener</A></B>(<A HREF="../../../../org/jivesoftware/smack/util/ReaderListener.html" title="interface in org.jivesoftware.smack.util">ReaderListener</A>&nbsp;readerListener)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Removes a reader listener from this reader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#reset()">reset</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/ObservableReader.html#skip(long)">skip</A></B>(long&nbsp;n)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.Reader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#read(java.nio.CharBuffer)" title="class or interface in java.io">read</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#clone()" title="class or interface in java.lang">clone</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#equals(java.lang.Object)" title="class or interface in java.lang">equals</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#finalize()" title="class or interface in java.lang">finalize</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#getClass()" title="class or interface in java.lang">getClass</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#hashCode()" title="class or interface in java.lang">hashCode</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notify()" title="class or interface in java.lang">notify</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notifyAll()" title="class or interface in java.lang">notifyAll</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#toString()" title="class or interface in java.lang">toString</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait()" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long)" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long, int)" title="class or interface in java.lang">wait</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ObservableReader(java.io.Reader)"><!-- --></A><H3>
ObservableReader</H3>
<PRE>
public <B>ObservableReader</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A>&nbsp;wrappedReader)</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="read(char[], int, int)"><!-- --></A><H3>
read</H3>
<PRE>
public int <B>read</B>(char[]&nbsp;cbuf,
                int&nbsp;off,
                int&nbsp;len)
         throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#read(char[], int, int)" title="class or interface in java.io">read</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Closeable.html?is-external=true#close()" title="class or interface in java.io">close</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</A></CODE><DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#close()" title="class or interface in java.io">close</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="read()"><!-- --></A><H3>
read</H3>
<PRE>
public int <B>read</B>()
         throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#read()" title="class or interface in java.io">read</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="read(char[])"><!-- --></A><H3>
read</H3>
<PRE>
public int <B>read</B>(char[]&nbsp;cbuf)
         throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#read(char[])" title="class or interface in java.io">read</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="skip(long)"><!-- --></A><H3>
skip</H3>
<PRE>
public long <B>skip</B>(long&nbsp;n)
          throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#skip(long)" title="class or interface in java.io">skip</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="ready()"><!-- --></A><H3>
ready</H3>
<PRE>
public boolean <B>ready</B>()
              throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#ready()" title="class or interface in java.io">ready</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="markSupported()"><!-- --></A><H3>
markSupported</H3>
<PRE>
public boolean <B>markSupported</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#markSupported()" title="class or interface in java.io">markSupported</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="mark(int)"><!-- --></A><H3>
mark</H3>
<PRE>
public void <B>mark</B>(int&nbsp;readAheadLimit)
          throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#mark(int)" title="class or interface in java.io">mark</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="reset()"><!-- --></A><H3>
reset</H3>
<PRE>
public void <B>reset</B>()
           throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true#reset()" title="class or interface in java.io">reset</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Reader.html?is-external=true" title="class or interface in java.io">Reader</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="addReaderListener(org.jivesoftware.smack.util.ReaderListener)"><!-- --></A><H3>
addReaderListener</H3>
<PRE>
public void <B>addReaderListener</B>(<A HREF="../../../../org/jivesoftware/smack/util/ReaderListener.html" title="interface in org.jivesoftware.smack.util">ReaderListener</A>&nbsp;readerListener)</PRE>
<DL>
<DD>Adds a reader listener to this reader that will be notified when
 new strings are read.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>readerListener</CODE> - a reader listener.</DL>
</DD>
</DL>
<HR>

<A NAME="removeReaderListener(org.jivesoftware.smack.util.ReaderListener)"><!-- --></A><H3>
removeReaderListener</H3>
<PRE>
public void <B>removeReaderListener</B>(<A HREF="../../../../org/jivesoftware/smack/util/ReaderListener.html" title="interface in org.jivesoftware.smack.util">ReaderListener</A>&nbsp;readerListener)</PRE>
<DL>
<DD>Removes a reader listener from this reader.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>readerListener</CODE> - a reader listener.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/DNSUtil.HostAddress.html" title="class in org.jivesoftware.smack.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/ObservableWriter.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/ObservableReader.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ObservableReader.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.io.Reader">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>
<i>Copyright &copy; 2003-2007 Jive Software. </i>
</BODY>
</HTML>
