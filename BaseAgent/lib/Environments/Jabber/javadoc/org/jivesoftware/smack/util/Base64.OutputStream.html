<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.6.0_20) on Mon Jul 04 15:12:25 CDT 2011 -->
<TITLE>
Base64.OutputStream (Smack 3.2.1 Documentation)
</TITLE>

<META NAME="date" CONTENT="2011-07-04">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Base64.OutputStream (Smack 3.2.1 Documentation)";
    }
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">
<HR>


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Base64.InputStream.html" title="class in org.jivesoftware.smack.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/Base64.OutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Base64.OutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.io.FilterOutputStream">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.jivesoftware.smack.util</FONT>
<BR>
Class Base64.OutputStream</H2>
<PRE>
<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</A>
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">java.io.OutputStream</A>
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">java.io.FilterOutputStream</A>
          <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.jivesoftware.smack.util.Base64.OutputStream</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Flushable.html?is-external=true" title="class or interface in java.io">Flushable</A></DD>
</DL>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../org/jivesoftware/smack/util/Base64.html" title="class in org.jivesoftware.smack.util">Base64</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public static class <B>Base64.OutputStream</B><DT>extends <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">FilterOutputStream</A></DL>
</PRE>

<P>
A <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.OutputStream</CODE></A> will write data to another
 <tt>java.io.OutputStream</tt>, given in the constructor,
 and encode/decode to/from Base64 notation on the fly.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.3</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/jivesoftware/smack/util/Base64.html" title="class in org.jivesoftware.smack.util"><CODE>Base64</CODE></A></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.FilterOutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">FilterOutputStream</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true#out" title="class or interface in java.io">out</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#Base64.OutputStream(java.io.OutputStream)">Base64.OutputStream</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</A>&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.OutputStream</CODE></A> in ENCODE mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#Base64.OutputStream(java.io.OutputStream, int)">Base64.OutputStream</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</A>&nbsp;out,
                    int&nbsp;options)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.OutputStream</CODE></A> in
 either ENCODE or DECODE mode.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Flushes and closes (I think, in the superclass) the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#flushBase64()">flushBase64</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Method added by PHIL.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#resumeEncoding()">resumeEncoding</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Resumes encoding of the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#suspendEncoding()">suspendEncoding</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Suspends encoding of the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#write(byte[], int, int)">write</A></B>(byte[]&nbsp;theBytes,
      int&nbsp;off,
      int&nbsp;len)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Calls <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#write(int)"><CODE>write(int)</CODE></A> repeatedly until <var>len</var>
 bytes are written.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#write(int)">write</A></B>(int&nbsp;theByte)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes the byte to the output stream after
 converting to/from Base64 notation.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.FilterOutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">FilterOutputStream</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true#flush()" title="class or interface in java.io">flush</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true#write(byte[])" title="class or interface in java.io">write</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#clone()" title="class or interface in java.lang">clone</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#equals(java.lang.Object)" title="class or interface in java.lang">equals</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#finalize()" title="class or interface in java.lang">finalize</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#getClass()" title="class or interface in java.lang">getClass</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#hashCode()" title="class or interface in java.lang">hashCode</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notify()" title="class or interface in java.lang">notify</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notifyAll()" title="class or interface in java.lang">notifyAll</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#toString()" title="class or interface in java.lang">toString</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait()" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long)" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long, int)" title="class or interface in java.lang">wait</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Base64.OutputStream(java.io.OutputStream)"><!-- --></A><H3>
Base64.OutputStream</H3>
<PRE>
public <B>Base64.OutputStream</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</A>&nbsp;out)</PRE>
<DL>
<DD>Constructs a <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.OutputStream</CODE></A> in ENCODE mode.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the <tt>java.io.OutputStream</tt> to which data will be written.<DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DL>
<HR>

<A NAME="Base64.OutputStream(java.io.OutputStream, int)"><!-- --></A><H3>
Base64.OutputStream</H3>
<PRE>
public <B>Base64.OutputStream</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</A>&nbsp;out,
                           int&nbsp;options)</PRE>
<DL>
<DD>Constructs a <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.OutputStream</CODE></A> in
 either ENCODE or DECODE mode.
 <p>
 Valid options:<pre>
   ENCODE or DECODE: Encode or Decode as data is read.
   DONT_BREAK_LINES: don't break lines at 76 characters
     (only meaningful when encoding)
     <i>Note: Technically, this makes your encoding non-compliant.</i>
 </pre>
 <p>
 Example: <code>new Base64.OutputStream( out, Base64.ENCODE )</code>
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the <tt>java.io.OutputStream</tt> to which data will be written.<DD><CODE>options</CODE> - Specified options.<DT><B>Since:</B></DT>
  <DD>1.3</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#ENCODE"><CODE>Base64.ENCODE</CODE></A>, 
<A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DECODE"><CODE>Base64.DECODE</CODE></A>, 
<A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DONT_BREAK_LINES"><CODE>Base64.DONT_BREAK_LINES</CODE></A></DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="write(int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(int&nbsp;theByte)
           throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD>Writes the byte to the output stream after
 converting to/from Base64 notation.
 When encoding, bytes are buffered three
 at a time before the output stream actually
 gets a write() call.
 When decoding, bytes are buffered four
 at a time.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true#write(int)" title="class or interface in java.io">write</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">FilterOutputStream</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>theByte</CODE> - the byte to write
<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE><DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="write(byte[], int, int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(byte[]&nbsp;theBytes,
                  int&nbsp;off,
                  int&nbsp;len)
           throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD>Calls <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html#write(int)"><CODE>write(int)</CODE></A> repeatedly until <var>len</var>
 bytes are written.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true#write(byte[], int, int)" title="class or interface in java.io">write</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">FilterOutputStream</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>theBytes</CODE> - array from which to read bytes<DD><CODE>off</CODE> - offset for array<DD><CODE>len</CODE> - max number of bytes to read into array
<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE><DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="flushBase64()"><!-- --></A><H3>
flushBase64</H3>
<PRE>
public void <B>flushBase64</B>()
                 throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD>Method added by PHIL. [Thanks, PHIL. -Rob]
 This pads the buffer without closing the stream.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE></DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD>Flushes and closes (I think, in the superclass) the stream.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Closeable.html?is-external=true#close()" title="class or interface in java.io">close</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true#close()" title="class or interface in java.io">close</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/FilterOutputStream.html?is-external=true" title="class or interface in java.io">FilterOutputStream</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE><DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="suspendEncoding()"><!-- --></A><H3>
suspendEncoding</H3>
<PRE>
public void <B>suspendEncoding</B>()
                     throws <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></PRE>
<DL>
<DD>Suspends encoding of the stream.
 May be helpful if you need to embed a piece of
 base640-encoded data in a stream.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</A></CODE><DT><B>Since:</B></DT>
  <DD>1.5.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="resumeEncoding()"><!-- --></A><H3>
resumeEncoding</H3>
<PRE>
public void <B>resumeEncoding</B>()</PRE>
<DL>
<DD>Resumes encoding of the stream.
 May be helpful if you need to embed a piece of
 base640-encoded data in a stream.
<P>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>1.5.1</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Base64.InputStream.html" title="class in org.jivesoftware.smack.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/Base64.OutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Base64.OutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.io.FilterOutputStream">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>
<i>Copyright &copy; 2003-2007 Jive Software. </i>
</BODY>
</HTML>
