<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.6.0_20) on Mon Jul 04 15:12:25 CDT 2011 -->
<TITLE>
XMPPError (Smack 3.2.1 Documentation)
</TITLE>

<META NAME="date" CONTENT="2011-07-04">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="XMPPError (Smack 3.2.1 Documentation)";
    }
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">
<HR>


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/packet/StreamError.html" title="class in org.jivesoftware.smack.packet"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/packet/XMPPError.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XMPPError.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.jivesoftware.smack.packet</FONT>
<BR>
Class XMPPError</H2>
<PRE>
<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</A>
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.jivesoftware.smack.packet.XMPPError</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>XMPPError</B><DT>extends <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></DL>
</PRE>

<P>
Represents a XMPP error sub-packet. Typically, a server responds to a request that has
 problems by sending the packet back and including an error packet. Each error has a code, type, 
 error condition as well as as an optional text explanation. Typical errors are:<p>

 <table border=1>
      <hr><td><b>Code</b></td><td><b>XMPP Error</b></td><td><b>Type</b></td></hr>
      <tr><td>500</td><td>interna-server-error</td><td>WAIT</td></tr>
      <tr><td>403</td><td>forbidden</td><td>AUTH</td></tr>
      <tr><td>400</td<td>bad-request</td><td>MODIFY</td>></tr>
      <tr><td>404</td><td>item-not-found</td><td>CANCEL</td></tr>
      <tr><td>409</td><td>conflict</td><td>CANCEL</td></tr>
      <tr><td>501</td><td>feature-not-implemented</td><td>CANCEL</td></tr>
      <tr><td>302</td><td>gone</td><td>MODIFY</td></tr>
      <tr><td>400</td><td>jid-malformed</td><td>MODIFY</td></tr>
      <tr><td>406</td><td>no-acceptable</td><td> MODIFY</td></tr>
      <tr><td>405</td><td>not-allowed</td><td>CANCEL</td></tr>
      <tr><td>401</td><td>not-authorized</td><td>AUTH</td></tr>
      <tr><td>402</td><td>payment-required</td><td>AUTH</td></tr>
      <tr><td>404</td><td>recipient-unavailable</td><td>WAIT</td></tr>
      <tr><td>302</td><td>redirect</td><td>MODIFY</td></tr>
      <tr><td>407</td><td>registration-required</td><td>AUTH</td></tr>
      <tr><td>404</td><td>remote-server-not-found</td><td>CANCEL</td></tr>
      <tr><td>504</td><td>remote-server-timeout</td><td>WAIT</td></tr>
      <tr><td>502</td><td>remote-server-error</td><td>CANCEL</td></tr>
      <tr><td>500</td><td>resource-constraint</td><td>WAIT</td></tr>
      <tr><td>503</td><td>service-unavailable</td><td>CANCEL</td></tr>
      <tr><td>407</td><td>subscription-required</td><td>AUTH</td></tr>
      <tr><td>500</td><td>undefined-condition</td><td>WAIT</td></tr>
      <tr><td>400</td><td>unexpected-condition</td><td>WAIT</td></tr>
      <tr><td>408</td><td>request-timeout</td><td>CANCEL</td></tr>
 </table>
<P>

<P>
<DL>
<DT><B>Author:</B></DT>
  <DD>Matt Tucker</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet">XMPPError.Condition</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A class to represent predefined error conditions.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Type.html" title="enum in org.jivesoftware.smack.packet">XMPPError.Type</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A class to represent the type of the Error.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#XMPPError(int)">XMPPError</A></B>(int&nbsp;code)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>new errors should be created using the constructor XMPPError(condition)</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#XMPPError(int, java.lang.String)">XMPPError</A></B>(int&nbsp;code,
          <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;message)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>new errors should be created using the constructor XMPPError(condition, message)</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#XMPPError(int, org.jivesoftware.smack.packet.XMPPError.Type, java.lang.String, java.lang.String, java.util.List)">XMPPError</A></B>(int&nbsp;code,
          <A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Type.html" title="enum in org.jivesoftware.smack.packet">XMPPError.Type</A>&nbsp;type,
          <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;condition,
          <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;message,
          <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</A>&lt;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&gt;&nbsp;extension)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new error with the specified code, type, condition and message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#XMPPError(org.jivesoftware.smack.packet.XMPPError.Condition)">XMPPError</A></B>(<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet">XMPPError.Condition</A>&nbsp;condition)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new error with the specified condition infering the type and code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#XMPPError(org.jivesoftware.smack.packet.XMPPError.Condition, java.lang.String)">XMPPError</A></B>(<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet">XMPPError.Condition</A>&nbsp;condition,
          <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;messageText)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new error with the specified condition and message infering the type and code.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#addExtension(org.jivesoftware.smack.packet.PacketExtension)">addExtension</A></B>(<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&nbsp;extension)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a packet extension to the error.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#getCode()">getCode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the error code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#getCondition()">getCondition</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the error condition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#getExtension(java.lang.String, java.lang.String)">getExtension</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;elementName,
             <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;namespace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the first patcket extension that matches the specified element name and
 namespace, or <tt>null</tt> if it doesn't exist.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</A>&lt;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&gt;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#getExtensions()">getExtensions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an Iterator for the error extensions attached to the xmppError.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#getMessage()">getMessage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the message describing the error, or null if there is no message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Type.html" title="enum in org.jivesoftware.smack.packet">XMPPError.Type</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#getType()">getType</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the error type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#setExtension(java.util.List)">setExtension</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</A>&lt;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&gt;&nbsp;extension)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the packet extension to the error.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.html#toXML()">toXML</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the error as XML.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#clone()" title="class or interface in java.lang">clone</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#equals(java.lang.Object)" title="class or interface in java.lang">equals</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#finalize()" title="class or interface in java.lang">finalize</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#getClass()" title="class or interface in java.lang">getClass</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#hashCode()" title="class or interface in java.lang">hashCode</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notify()" title="class or interface in java.lang">notify</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notifyAll()" title="class or interface in java.lang">notifyAll</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait()" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long)" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long, int)" title="class or interface in java.lang">wait</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="XMPPError(org.jivesoftware.smack.packet.XMPPError.Condition)"><!-- --></A><H3>
XMPPError</H3>
<PRE>
public <B>XMPPError</B>(<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet">XMPPError.Condition</A>&nbsp;condition)</PRE>
<DL>
<DD>Creates a new error with the specified condition infering the type and code.
 If the Condition is predefined, client code should be like:
     new XMPPError(XMPPError.Condition.remote_server_timeout);
 If the Condition is not predefined, invocations should be like 
     new XMPPError(new XMPPError.Condition("my_own_error"));
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>condition</CODE> - the error condition.</DL>
</DL>
<HR>

<A NAME="XMPPError(org.jivesoftware.smack.packet.XMPPError.Condition, java.lang.String)"><!-- --></A><H3>
XMPPError</H3>
<PRE>
public <B>XMPPError</B>(<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet">XMPPError.Condition</A>&nbsp;condition,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;messageText)</PRE>
<DL>
<DD>Creates a new error with the specified condition and message infering the type and code.
 If the Condition is predefined, client code should be like:
     new XMPPError(XMPPError.Condition.remote_server_timeout, "Error Explanation");
 If the Condition is not predefined, invocations should be like 
     new XMPPError(new XMPPError.Condition("my_own_error"), "Error Explanation");
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>condition</CODE> - the error condition.<DD><CODE>messageText</CODE> - a message describing the error.</DL>
</DL>
<HR>

<A NAME="XMPPError(int)"><!-- --></A><H3>
XMPPError</H3>
<PRE>
public <B>XMPPError</B>(int&nbsp;code)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>new errors should be created using the constructor XMPPError(condition)</I>
<P>
<DD>Creates a new  error with the specified code and no message.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>code</CODE> - the error code.</DL>
</DL>
<HR>

<A NAME="XMPPError(int, java.lang.String)"><!-- --></A><H3>
XMPPError</H3>
<PRE>
public <B>XMPPError</B>(int&nbsp;code,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;message)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>new errors should be created using the constructor XMPPError(condition, message)</I>
<P>
<DD>Creates a new error with the specified code and message.
 deprecated
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>code</CODE> - the error code.<DD><CODE>message</CODE> - a message describing the error.</DL>
</DL>
<HR>

<A NAME="XMPPError(int, org.jivesoftware.smack.packet.XMPPError.Type, java.lang.String, java.lang.String, java.util.List)"><!-- --></A><H3>
XMPPError</H3>
<PRE>
public <B>XMPPError</B>(int&nbsp;code,
                 <A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Type.html" title="enum in org.jivesoftware.smack.packet">XMPPError.Type</A>&nbsp;type,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;condition,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;message,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</A>&lt;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&gt;&nbsp;extension)</PRE>
<DL>
<DD>Creates a new error with the specified code, type, condition and message.
 This constructor is used when the condition is not recognized automatically by XMPPError
 i.e. there is not a defined instance of ErrorCondition or it does not applies the default 
 specification.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>code</CODE> - the error code.<DD><CODE>type</CODE> - the error type.<DD><CODE>condition</CODE> - the error condition.<DD><CODE>message</CODE> - a message describing the error.<DD><CODE>extension</CODE> - list of packet extensions</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getCondition()"><!-- --></A><H3>
getCondition</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>getCondition</B>()</PRE>
<DL>
<DD>Returns the error condition.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the error condition.</DL>
</DD>
</DL>
<HR>

<A NAME="getType()"><!-- --></A><H3>
getType</H3>
<PRE>
public <A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Type.html" title="enum in org.jivesoftware.smack.packet">XMPPError.Type</A> <B>getType</B>()</PRE>
<DL>
<DD>Returns the error type.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the error type.</DL>
</DD>
</DL>
<HR>

<A NAME="getCode()"><!-- --></A><H3>
getCode</H3>
<PRE>
public int <B>getCode</B>()</PRE>
<DL>
<DD>Returns the error code.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the error code.</DL>
</DD>
</DL>
<HR>

<A NAME="getMessage()"><!-- --></A><H3>
getMessage</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>getMessage</B>()</PRE>
<DL>
<DD>Returns the message describing the error, or null if there is no message.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the message describing the error, or null if there is no message.</DL>
</DD>
</DL>
<HR>

<A NAME="toXML()"><!-- --></A><H3>
toXML</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>toXML</B>()</PRE>
<DL>
<DD>Returns the error as XML.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the error as XML.</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>toString</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#toString()" title="class or interface in java.lang">toString</A></CODE> in class <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getExtensions()"><!-- --></A><H3>
getExtensions</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</A>&lt;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&gt; <B>getExtensions</B>()</PRE>
<DL>
<DD>Returns an Iterator for the error extensions attached to the xmppError.
 An application MAY provide application-specific error information by including a 
 properly-namespaced child in the error element.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an Iterator for the error extensions.</DL>
</DD>
</DL>
<HR>

<A NAME="getExtension(java.lang.String, java.lang.String)"><!-- --></A><H3>
getExtension</H3>
<PRE>
public <A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A> <B>getExtension</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;elementName,
                                    <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;namespace)</PRE>
<DL>
<DD>Returns the first patcket extension that matches the specified element name and
 namespace, or <tt>null</tt> if it doesn't exist.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>elementName</CODE> - the XML element name of the packet extension.<DD><CODE>namespace</CODE> - the XML element namespace of the packet extension.
<DT><B>Returns:</B><DD>the extension, or <tt>null</tt> if it doesn't exist.</DL>
</DD>
</DL>
<HR>

<A NAME="addExtension(org.jivesoftware.smack.packet.PacketExtension)"><!-- --></A><H3>
addExtension</H3>
<PRE>
public void <B>addExtension</B>(<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&nbsp;extension)</PRE>
<DL>
<DD>Adds a packet extension to the error.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>extension</CODE> - a packet extension.</DL>
</DD>
</DL>
<HR>

<A NAME="setExtension(java.util.List)"><!-- --></A><H3>
setExtension</H3>
<PRE>
public void <B>setExtension</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</A>&lt;<A HREF="../../../../org/jivesoftware/smack/packet/PacketExtension.html" title="interface in org.jivesoftware.smack.packet">PacketExtension</A>&gt;&nbsp;extension)</PRE>
<DL>
<DD>Set the packet extension to the error.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>extension</CODE> - a packet extension.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/packet/StreamError.html" title="class in org.jivesoftware.smack.packet"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/packet/XMPPError.Condition.html" title="class in org.jivesoftware.smack.packet"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/packet/XMPPError.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XMPPError.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>
<i>Copyright &copy; 2003-2007 Jive Software. </i>
</BODY>
</HTML>
