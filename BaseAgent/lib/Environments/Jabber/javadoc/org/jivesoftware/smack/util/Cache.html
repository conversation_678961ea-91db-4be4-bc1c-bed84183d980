<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.6.0_20) on Mon Jul 04 15:12:25 CDT 2011 -->
<TITLE>
Cache (Smack 3.2.1 Documentation)
</TITLE>

<META NAME="date" CONTENT="2011-07-04">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Cache (Smack 3.2.1 Documentation)";
    }
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">
<HR>


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/DNSUtil.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/Cache.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Cache.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.jivesoftware.smack.util</FONT>
<BR>
Class Cache&lt;K,V&gt;</H2>
<PRE>
<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</A>
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.jivesoftware.smack.util.Cache&lt;K,V&gt;</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;K,V&gt;</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Cache&lt;K,V&gt;</B><DT>extends <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A><DT>implements <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;K,V&gt;</DL>
</PRE>

<P>
A specialized Map that is size-limited (using an LRU algorithm) and
 has an optional expiration time for cache items. The Map is thread-safe.<p>

 The algorithm for cache is as follows: a HashMap is maintained for fast
 object lookup. Two linked lists are maintained: one keeps objects in the
 order they are accessed from cache, the other keeps objects in the order
 they were originally added to cache. When objects are added to cache, they
 are first wrapped by a CacheObject which maintains the following pieces
 of information:<ul>
 <li> A pointer to the node in the linked list that maintains accessed
 order for the object. Keeping a reference to the node lets us avoid
 linear scans of the linked list.
 <li> A pointer to the node in the linked list that maintains the age
 of the object in cache. Keeping a reference to the node lets us avoid
 linear scans of the linked list.</ul>
 <p/>
 To get an object from cache, a hash lookup is performed to get a reference
 to the CacheObject that wraps the real object we are looking for.
 The object is subsequently moved to the front of the accessed linked list
 and any necessary cache cleanups are performed. Cache deletion and expiration
 is performed as needed.
<P>

<P>
<DL>
<DT><B>Author:</B></DT>
  <DD>Matt Tucker</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="nested_classes_inherited_from_class_java.util.Map"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Nested classes/interfaces inherited from interface java.util.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.Entry.html?is-external=true" title="class or interface in java.util">Map.Entry</A>&lt;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.Entry.html?is-external=true" title="class or interface in java.util">K</A>,<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.Entry.html?is-external=true" title="class or interface in java.util">V</A>&gt;</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.jivesoftware.smack.util.Cache.LinkedList</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#ageList">ageList</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Linked list to maintain time that cache objects were initially added
 to the cache, most recently added to oldest added.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#cacheHits">cacheHits</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maintain the number of cache hits and misses.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#cacheMisses">cacheMisses</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maintain the number of cache hits and misses.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.jivesoftware.smack.util.Cache.LinkedList</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#lastAccessedList">lastAccessedList</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Linked list to maintain order that cache objects are accessed
 in, most used to least used.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,org.jivesoftware.smack.util.Cache.CacheObject&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;&gt;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#map">map</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The map the keys and values are stored in.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#maxCacheSize">maxCacheSize</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maximum number of items the cache will hold.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#maxLifetime">maxLifetime</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maximum length of time objects can exist in cache before expiring.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#Cache(int, long)">Cache</A></B>(int&nbsp;maxSize,
      long&nbsp;maxLifetime)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a new cache and specify the maximum size of for the cache in
 bytes, and the maximum lifetime of objects.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#clear()">clear</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#containsKey(java.lang.Object)">containsKey</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#containsValue(java.lang.Object)">containsValue</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#cullCache()">cullCache</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Removes the least recently used elements if the cache size is greater than
 or equal to the maximum allowed size until the cache is at least 10% empty.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#deleteExpiredEntries()">deleteExpiredEntries</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clears all entries out of cache where the entries are older than the
 maximum defined age.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</A>&lt;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.Entry.html?is-external=true" title="class or interface in java.util">Map.Entry</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;&gt;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#entrySet()">entrySet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#get(java.lang.Object)">get</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#getCacheHits()">getCacheHits</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#getCacheMisses()">getCacheMisses</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#getMaxCacheSize()">getMaxCacheSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#getMaxLifetime()">getMaxLifetime</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#isEmpty()">isEmpty</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>&gt;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#keySet()">keySet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#put(K, V)">put</A></B>(<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>&nbsp;key,
    <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#putAll(java.util.Map)">putAll</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;? extends <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,? extends <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;&nbsp;map)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#remove(java.lang.Object)">remove</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#remove(java.lang.Object, boolean)">remove</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key,
       boolean&nbsp;internal)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#setMaxCacheSize(int)">setMaxCacheSize</A></B>(int&nbsp;maxCacheSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#setMaxLifetime(long)">setMaxLifetime</A></B>(long&nbsp;maxLifetime)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#size()">size</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Cache.html#values()">values</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#clone()" title="class or interface in java.lang">clone</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#equals(java.lang.Object)" title="class or interface in java.lang">equals</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#finalize()" title="class or interface in java.lang">finalize</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#getClass()" title="class or interface in java.lang">getClass</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#hashCode()" title="class or interface in java.lang">hashCode</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notify()" title="class or interface in java.lang">notify</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notifyAll()" title="class or interface in java.lang">notifyAll</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#toString()" title="class or interface in java.lang">toString</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait()" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long)" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long, int)" title="class or interface in java.lang">wait</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.util.Map"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from interface java.util.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#equals(java.lang.Object)" title="class or interface in java.util">equals</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#hashCode()" title="class or interface in java.util">hashCode</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="map"><!-- --></A><H3>
map</H3>
<PRE>
protected <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,org.jivesoftware.smack.util.Cache.CacheObject&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;&gt; <B>map</B></PRE>
<DL>
<DD>The map the keys and values are stored in.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="lastAccessedList"><!-- --></A><H3>
lastAccessedList</H3>
<PRE>
protected org.jivesoftware.smack.util.Cache.LinkedList <B>lastAccessedList</B></PRE>
<DL>
<DD>Linked list to maintain order that cache objects are accessed
 in, most used to least used.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="ageList"><!-- --></A><H3>
ageList</H3>
<PRE>
protected org.jivesoftware.smack.util.Cache.LinkedList <B>ageList</B></PRE>
<DL>
<DD>Linked list to maintain time that cache objects were initially added
 to the cache, most recently added to oldest added.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="maxCacheSize"><!-- --></A><H3>
maxCacheSize</H3>
<PRE>
protected int <B>maxCacheSize</B></PRE>
<DL>
<DD>Maximum number of items the cache will hold.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="maxLifetime"><!-- --></A><H3>
maxLifetime</H3>
<PRE>
protected long <B>maxLifetime</B></PRE>
<DL>
<DD>Maximum length of time objects can exist in cache before expiring.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="cacheHits"><!-- --></A><H3>
cacheHits</H3>
<PRE>
protected long <B>cacheHits</B></PRE>
<DL>
<DD>Maintain the number of cache hits and misses. A cache hit occurs every
 time the get method is called and the cache contains the requested
 object. A cache miss represents the opposite occurence.<p>

 Keeping track of cache hits and misses lets one measure how efficient
 the cache is; the higher the percentage of hits, the more efficient.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="cacheMisses"><!-- --></A><H3>
cacheMisses</H3>
<PRE>
protected long <B>cacheMisses</B></PRE>
<DL>
<DD>Maintain the number of cache hits and misses. A cache hit occurs every
 time the get method is called and the cache contains the requested
 object. A cache miss represents the opposite occurence.<p>

 Keeping track of cache hits and misses lets one measure how efficient
 the cache is; the higher the percentage of hits, the more efficient.
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Cache(int, long)"><!-- --></A><H3>
Cache</H3>
<PRE>
public <B>Cache</B>(int&nbsp;maxSize,
             long&nbsp;maxLifetime)</PRE>
<DL>
<DD>Create a new cache and specify the maximum size of for the cache in
 bytes, and the maximum lifetime of objects.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>maxSize</CODE> - the maximum number of objects the cache will hold. -1
      means the cache has no max size.<DD><CODE>maxLifetime</CODE> - the maximum amount of time (in ms) objects can exist in
      cache before being deleted. -1 means objects never expire.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="put(java.lang.Object,java.lang.Object)"><!-- --></A><A NAME="put(K, V)"><!-- --></A><H3>
put</H3>
<PRE>
public <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A> <B>put</B>(<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>&nbsp;key,
             <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&nbsp;value)</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#put(K, V)" title="class or interface in java.util">put</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="get(java.lang.Object)"><!-- --></A><H3>
get</H3>
<PRE>
public <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A> <B>get</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key)</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#get(java.lang.Object)" title="class or interface in java.util">get</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="remove(java.lang.Object)"><!-- --></A><H3>
remove</H3>
<PRE>
public <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A> <B>remove</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key)</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#remove(java.lang.Object)" title="class or interface in java.util">remove</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="remove(java.lang.Object, boolean)"><!-- --></A><H3>
remove</H3>
<PRE>
public <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A> <B>remove</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key,
                boolean&nbsp;internal)</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="clear()"><!-- --></A><H3>
clear</H3>
<PRE>
public void <B>clear</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#clear()" title="class or interface in java.util">clear</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="size()"><!-- --></A><H3>
size</H3>
<PRE>
public int <B>size</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#size()" title="class or interface in java.util">size</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="isEmpty()"><!-- --></A><H3>
isEmpty</H3>
<PRE>
public boolean <B>isEmpty</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#isEmpty()" title="class or interface in java.util">isEmpty</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="values()"><!-- --></A><H3>
values</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt; <B>values</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#values()" title="class or interface in java.util">values</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="containsKey(java.lang.Object)"><!-- --></A><H3>
containsKey</H3>
<PRE>
public boolean <B>containsKey</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;key)</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#containsKey(java.lang.Object)" title="class or interface in java.util">containsKey</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="putAll(java.util.Map)"><!-- --></A><H3>
putAll</H3>
<PRE>
public void <B>putAll</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;? extends <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,? extends <A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;&nbsp;map)</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#putAll(java.util.Map)" title="class or interface in java.util">putAll</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="containsValue(java.lang.Object)"><!-- --></A><H3>
containsValue</H3>
<PRE>
public boolean <B>containsValue</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A>&nbsp;value)</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#containsValue(java.lang.Object)" title="class or interface in java.util">containsValue</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="entrySet()"><!-- --></A><H3>
entrySet</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</A>&lt;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.Entry.html?is-external=true" title="class or interface in java.util">Map.Entry</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;&gt; <B>entrySet</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#entrySet()" title="class or interface in java.util">entrySet</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="keySet()"><!-- --></A><H3>
keySet</H3>
<PRE>
public <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util">Set</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>&gt; <B>keySet</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true#keySet()" title="class or interface in java.util">keySet</A></CODE> in interface <CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</A>&lt;<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">K</A>,<A HREF="../../../../org/jivesoftware/smack/util/Cache.html" title="type parameter in Cache">V</A>&gt;</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCacheHits()"><!-- --></A><H3>
getCacheHits</H3>
<PRE>
public long <B>getCacheHits</B>()</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCacheMisses()"><!-- --></A><H3>
getCacheMisses</H3>
<PRE>
public long <B>getCacheMisses</B>()</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getMaxCacheSize()"><!-- --></A><H3>
getMaxCacheSize</H3>
<PRE>
public int <B>getMaxCacheSize</B>()</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="setMaxCacheSize(int)"><!-- --></A><H3>
setMaxCacheSize</H3>
<PRE>
public void <B>setMaxCacheSize</B>(int&nbsp;maxCacheSize)</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getMaxLifetime()"><!-- --></A><H3>
getMaxLifetime</H3>
<PRE>
public long <B>getMaxLifetime</B>()</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="setMaxLifetime(long)"><!-- --></A><H3>
setMaxLifetime</H3>
<PRE>
public void <B>setMaxLifetime</B>(long&nbsp;maxLifetime)</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="deleteExpiredEntries()"><!-- --></A><H3>
deleteExpiredEntries</H3>
<PRE>
protected void <B>deleteExpiredEntries</B>()</PRE>
<DL>
<DD>Clears all entries out of cache where the entries are older than the
 maximum defined age.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="cullCache()"><!-- --></A><H3>
cullCache</H3>
<PRE>
protected void <B>cullCache</B>()</PRE>
<DL>
<DD>Removes the least recently used elements if the cache size is greater than
 or equal to the maximum allowed size until the cache is at least 10% empty.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/DNSUtil.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/Cache.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Cache.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>
<i>Copyright &copy; 2003-2007 Jive Software. </i>
</BODY>
</HTML>
