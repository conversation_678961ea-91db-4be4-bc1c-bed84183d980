<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.6.0_20) on Mon Jul 04 15:12:25 CDT 2011 -->
<TITLE>
Base64 (Smack 3.2.1 Documentation)
</TITLE>

<META NAME="date" CONTENT="2011-07-04">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Base64 (Smack 3.2.1 Documentation)";
    }
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">
<HR>


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Base64.InputStream.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/Base64.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Base64.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.jivesoftware.smack.util</FONT>
<BR>
Class Base64</H2>
<PRE>
<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</A>
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.jivesoftware.smack.util.Base64</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>Base64</B><DT>extends <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></DL>
</PRE>

<P>
<p>Encodes and decodes to and from Base64 notation.</p>
 <p>Homepage: <a href="http://iharder.net/base64">http://iharder.net/base64</a>.</p>

 <p>
 Change Log:
 </p>
 <ul>
  <li>v2.2.1 - Fixed bug using URL_SAFE and ORDERED encodings. Fixed bug
   when using very small files (~< 40 bytes).</li>
  <li>v2.2 - Added some helper methods for encoding/decoding directly from
   one file to the next. Also added a main() method to support command line
   encoding/decoding from one file to the next. Also added these Base64 dialects:
   <ol>
   <li>The default is RFC3548 format.</li>
   <li>Calling Base64.setFormat(Base64.BASE64_FORMAT.URLSAFE_FORMAT) generates
   URL and file name friendly format as described in Section 4 of RFC3548.
   http://www.faqs.org/rfcs/rfc3548.html</li>
   <li>Calling Base64.setFormat(Base64.BASE64_FORMAT.ORDERED_FORMAT) generates
   URL and file name friendly format that preserves lexical ordering as described
   in http://www.faqs.org/qa/rfcc-1940.html</li>
   </ol>
   Special thanks to Jim Kellerman at <a href="http://www.powerset.com/">http://www.powerset.com/</a>
   for contributing the new Base64 dialects.
  </li>

  <li>v2.1 - Cleaned up javadoc comments and unused variables and methods. Added
   some convenience methods for reading and writing to and from files.</li>
  <li>v2.0.2 - Now specifies UTF-8 encoding in places where the code fails on systems
   with other encodings (like EBCDIC).</li>
  <li>v2.0.1 - Fixed an error when decoding a single byte, that is, when the
   encoded data was a single byte.</li>
  <li>v2.0 - I got rid of methods that used booleans to set options.
   Now everything is more consolidated and cleaner. The code now detects
   when data that's being decoded is gzip-compressed and will decompress it
   automatically. Generally things are cleaner. You'll probably have to
   change some method calls that you were making to support the new
   options format (<tt>int</tt>s that you "OR" together).</li>
  <li>v1.5.1 - Fixed bug when decompressing and decoding to a
   byte[] using <tt>decode( String s, boolean gzipCompressed )</tt>.
   Added the ability to "suspend" encoding in the Output Stream so
   you can turn on and off the encoding if you need to embed base64
   data in an otherwise "normal" stream (like an XML file).</li>
  <li>v1.5 - Output stream pases on flush() command but doesn't do anything itself.
      This helps when using GZIP streams.
      Added the ability to GZip-compress objects before encoding them.</li>
  <li>v1.4 - Added helper methods to read/write files.</li>
  <li>v1.3.6 - Fixed OutputStream.flush() so that 'position' is reset.</li>
  <li>v1.3.5 - Added flag to turn on and off line breaks. Fixed bug in input stream
      where last buffer being read, if not completely full, was not returned.</li>
  <li>v1.3.4 - Fixed when "improperly padded stream" error was thrown at the wrong time.</li>
  <li>v1.3.3 - Fixed I/O streams which were totally messed up.</li>
 </ul>

 <p>
 I am placing this code in the Public Domain. Do with it as you will.
 This software comes with no guarantees or warranties but with
 plenty of well-wishing instead!
 Please visit <a href="http://iharder.net/base64">http://iharder.net/base64</a>
 periodically to check for updates or to contribute improvements.
 </p>
<P>

<P>
<DL>
<DT><B>Version:</B></DT>
  <DD>2.2.1</DD>
<DT><B>Author:</B></DT>
  <DD>Robert Harder, <EMAIL></DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.InputStream.html" title="class in org.jivesoftware.smack.util">Base64.InputStream</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A <A HREF="../../../../org/jivesoftware/smack/util/Base64.InputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.InputStream</CODE></A> will read data from another
 <tt>java.io.InputStream</tt>, given in the constructor,
 and encode/decode to/from Base64 notation on the fly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util">Base64.OutputStream</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A <A HREF="../../../../org/jivesoftware/smack/util/Base64.OutputStream.html" title="class in org.jivesoftware.smack.util"><CODE>Base64.OutputStream</CODE></A> will write data to another
 <tt>java.io.OutputStream</tt>, given in the constructor,
 and encode/decode to/from Base64 notation on the fly.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DECODE">DECODE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify decoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DONT_BREAK_LINES">DONT_BREAK_LINES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Don't break lines when encoding (violates strict Base64 specification)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#ENCODE">ENCODE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify encoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#GZIP">GZIP</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify that data should be gzip-compressed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#NO_OPTIONS">NO_OPTIONS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No options specified.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#ORDERED">ORDERED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encode using the special "ordered" dialect of Base64 described here:
 <a href="http://www.faqs.org/qa/rfcc-1940.html">http://www.faqs.org/qa/rfcc-1940.html</a>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#URL_SAFE">URL_SAFE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encode using Base64-like encoding that is URL- and Filename-safe as described
 in Section 4 of RFC3548:
 <a href="http://www.faqs.org/rfcs/rfc3548.html">http://www.faqs.org/rfcs/rfc3548.html</a>.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decode(byte[], int, int, int)">decode</A></B>(byte[]&nbsp;source,
       int&nbsp;off,
       int&nbsp;len,
       int&nbsp;options)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Very low-level access to decoding ASCII characters in
 the form of a byte array.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decode(java.lang.String)">decode</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;s)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decode(java.lang.String, int)">decode</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;s,
       int&nbsp;options)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decodeFileToFile(java.lang.String, java.lang.String)">decodeFileToFile</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;infile,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;outfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reads <tt>infile</tt> and decodes it to <tt>outfile</tt>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decodeFromFile(java.lang.String)">decodeFromFile</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method for reading a base64-encoded
 file and decoding it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decodeToFile(java.lang.String, java.lang.String)">decodeToFile</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;dataToDecode,
             <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method for decoding data to a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#decodeToObject(java.lang.String)">decodeToObject</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;encodedObject)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attempts to decode Base64 data and deserialize a Java
 Object within.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeBytes(byte[])">encodeBytes</A></B>(byte[]&nbsp;source)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encodes a byte array into Base64 notation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeBytes(byte[], int)">encodeBytes</A></B>(byte[]&nbsp;source,
            int&nbsp;options)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encodes a byte array into Base64 notation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeBytes(byte[], int, int)">encodeBytes</A></B>(byte[]&nbsp;source,
            int&nbsp;off,
            int&nbsp;len)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encodes a byte array into Base64 notation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeBytes(byte[], int, int, int)">encodeBytes</A></B>(byte[]&nbsp;source,
            int&nbsp;off,
            int&nbsp;len,
            int&nbsp;options)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encodes a byte array into Base64 notation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeFileToFile(java.lang.String, java.lang.String)">encodeFileToFile</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;infile,
                 <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;outfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reads <tt>infile</tt> and encodes it to <tt>outfile</tt>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeFromFile(java.lang.String)">encodeFromFile</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method for reading a binary file
 and base64-encoding it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeObject(java.io.Serializable)">encodeObject</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</A>&nbsp;serializableObject)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Serializes an object and returns the Base64-encoded
 version of that serialized object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeObject(java.io.Serializable, int)">encodeObject</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</A>&nbsp;serializableObject,
             int&nbsp;options)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Serializes an object and returns the Base64-encoded
 version of that serialized object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#encodeToFile(byte[], java.lang.String)">encodeToFile</A></B>(byte[]&nbsp;dataToEncode,
             <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method for encoding data to a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#main(java.lang.String[])">main</A></B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>[]&nbsp;args)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encodes or decodes two files from the command line;
 <strong>feel free to delete this method (in fact you probably should)
 if you're embedding this code into a larger program.</strong></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#clone()" title="class or interface in java.lang">clone</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#equals(java.lang.Object)" title="class or interface in java.lang">equals</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#finalize()" title="class or interface in java.lang">finalize</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#getClass()" title="class or interface in java.lang">getClass</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#hashCode()" title="class or interface in java.lang">hashCode</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notify()" title="class or interface in java.lang">notify</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#notifyAll()" title="class or interface in java.lang">notifyAll</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#toString()" title="class or interface in java.lang">toString</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait()" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long)" title="class or interface in java.lang">wait</A>, <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true#wait(long, int)" title="class or interface in java.lang">wait</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="NO_OPTIONS"><!-- --></A><H3>
NO_OPTIONS</H3>
<PRE>
public static final int <B>NO_OPTIONS</B></PRE>
<DL>
<DD>No options specified. Value is zero.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.NO_OPTIONS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENCODE"><!-- --></A><H3>
ENCODE</H3>
<PRE>
public static final int <B>ENCODE</B></PRE>
<DL>
<DD>Specify encoding.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.ENCODE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DECODE"><!-- --></A><H3>
DECODE</H3>
<PRE>
public static final int <B>DECODE</B></PRE>
<DL>
<DD>Specify decoding.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.DECODE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GZIP"><!-- --></A><H3>
GZIP</H3>
<PRE>
public static final int <B>GZIP</B></PRE>
<DL>
<DD>Specify that data should be gzip-compressed.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.GZIP">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DONT_BREAK_LINES"><!-- --></A><H3>
DONT_BREAK_LINES</H3>
<PRE>
public static final int <B>DONT_BREAK_LINES</B></PRE>
<DL>
<DD>Don't break lines when encoding (violates strict Base64 specification)
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.DONT_BREAK_LINES">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="URL_SAFE"><!-- --></A><H3>
URL_SAFE</H3>
<PRE>
public static final int <B>URL_SAFE</B></PRE>
<DL>
<DD>Encode using Base64-like encoding that is URL- and Filename-safe as described
 in Section 4 of RFC3548:
 <a href="http://www.faqs.org/rfcs/rfc3548.html">http://www.faqs.org/rfcs/rfc3548.html</a>.
 It is important to note that data encoded this way is <em>not</em> officially valid Base64,
 or at the very least should not be called Base64 without also specifying that is
 was encoded using the URL- and Filename-safe dialect.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.URL_SAFE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ORDERED"><!-- --></A><H3>
ORDERED</H3>
<PRE>
public static final int <B>ORDERED</B></PRE>
<DL>
<DD>Encode using the special "ordered" dialect of Base64 described here:
 <a href="http://www.faqs.org/qa/rfcc-1940.html">http://www.faqs.org/qa/rfcc-1940.html</a>.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.jivesoftware.smack.util.Base64.ORDERED">Constant Field Values</A></DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="main(java.lang.String[])"><!-- --></A><H3>
main</H3>
<PRE>
public static final void <B>main</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>[]&nbsp;args)</PRE>
<DL>
<DD>Encodes or decodes two files from the command line;
 <strong>feel free to delete this method (in fact you probably should)
 if you're embedding this code into a larger program.</strong>
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeObject(java.io.Serializable)"><!-- --></A><H3>
encodeObject</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeObject</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</A>&nbsp;serializableObject)</PRE>
<DL>
<DD>Serializes an object and returns the Base64-encoded
 version of that serialized object. If the object
 cannot be serialized or there is another error,
 the method will return <tt>null</tt>.
 The object is not GZip-compressed before being encoded.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>serializableObject</CODE> - The object to encode
<DT><B>Returns:</B><DD>The Base64-encoded object<DT><B>Since:</B></DT>
  <DD>1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeObject(java.io.Serializable, int)"><!-- --></A><H3>
encodeObject</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeObject</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</A>&nbsp;serializableObject,
                                  int&nbsp;options)</PRE>
<DL>
<DD>Serializes an object and returns the Base64-encoded
 version of that serialized object. If the object
 cannot be serialized or there is another error,
 the method will return <tt>null</tt>.
 <p>
 Valid options:<pre>
   GZIP: gzip-compresses object before encoding it.
   DONT_BREAK_LINES: don't break lines at 76 characters
     <i>Note: Technically, this makes your encoding non-compliant.</i>
 </pre>
 <p>
 Example: <code>encodeObject( myObj, Base64.GZIP )</code> or
 <p>
 Example: <code>encodeObject( myObj, Base64.GZIP | Base64.DONT_BREAK_LINES )</code>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>serializableObject</CODE> - The object to encode<DD><CODE>options</CODE> - Specified options
<DT><B>Returns:</B><DD>The Base64-encoded object<DT><B>Since:</B></DT>
  <DD>2.0</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#GZIP"><CODE>GZIP</CODE></A>, 
<A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DONT_BREAK_LINES"><CODE>DONT_BREAK_LINES</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="encodeBytes(byte[])"><!-- --></A><H3>
encodeBytes</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeBytes</B>(byte[]&nbsp;source)</PRE>
<DL>
<DD>Encodes a byte array into Base64 notation.
 Does not GZip-compress data.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - The data to convert<DT><B>Since:</B></DT>
  <DD>1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeBytes(byte[], int)"><!-- --></A><H3>
encodeBytes</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeBytes</B>(byte[]&nbsp;source,
                                 int&nbsp;options)</PRE>
<DL>
<DD>Encodes a byte array into Base64 notation.
 <p>
 Valid options:<pre>
   GZIP: gzip-compresses object before encoding it.
   DONT_BREAK_LINES: don't break lines at 76 characters
     <i>Note: Technically, this makes your encoding non-compliant.</i>
 </pre>
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP )</code> or
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP | Base64.DONT_BREAK_LINES )</code>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - The data to convert<DD><CODE>options</CODE> - Specified options<DT><B>Since:</B></DT>
  <DD>2.0</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#GZIP"><CODE>GZIP</CODE></A>, 
<A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DONT_BREAK_LINES"><CODE>DONT_BREAK_LINES</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="encodeBytes(byte[], int, int)"><!-- --></A><H3>
encodeBytes</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeBytes</B>(byte[]&nbsp;source,
                                 int&nbsp;off,
                                 int&nbsp;len)</PRE>
<DL>
<DD>Encodes a byte array into Base64 notation.
 Does not GZip-compress data.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - The data to convert<DD><CODE>off</CODE> - Offset in array where conversion should begin<DD><CODE>len</CODE> - Length of data to convert<DT><B>Since:</B></DT>
  <DD>1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeBytes(byte[], int, int, int)"><!-- --></A><H3>
encodeBytes</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeBytes</B>(byte[]&nbsp;source,
                                 int&nbsp;off,
                                 int&nbsp;len,
                                 int&nbsp;options)</PRE>
<DL>
<DD>Encodes a byte array into Base64 notation.
 <p>
 Valid options:<pre>
   GZIP: gzip-compresses object before encoding it.
   DONT_BREAK_LINES: don't break lines at 76 characters
     <i>Note: Technically, this makes your encoding non-compliant.</i>
 </pre>
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP )</code> or
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP | Base64.DONT_BREAK_LINES )</code>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - The data to convert<DD><CODE>off</CODE> - Offset in array where conversion should begin<DD><CODE>len</CODE> - Length of data to convert<DD><CODE>options</CODE> - Specified options; alphabet type is pulled from this (standard, url-safe, ordered)<DT><B>Since:</B></DT>
  <DD>2.0</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/jivesoftware/smack/util/Base64.html#GZIP"><CODE>GZIP</CODE></A>, 
<A HREF="../../../../org/jivesoftware/smack/util/Base64.html#DONT_BREAK_LINES"><CODE>DONT_BREAK_LINES</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="decode(byte[], int, int, int)"><!-- --></A><H3>
decode</H3>
<PRE>
public static byte[] <B>decode</B>(byte[]&nbsp;source,
                            int&nbsp;off,
                            int&nbsp;len,
                            int&nbsp;options)</PRE>
<DL>
<DD>Very low-level access to decoding ASCII characters in
 the form of a byte array. Does not support automatically
 gunzipping or any other "fancy" features.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - The Base64 encoded data<DD><CODE>off</CODE> - The offset of where to begin decoding<DD><CODE>len</CODE> - The length of characters to decode
<DT><B>Returns:</B><DD>decoded data<DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="decode(java.lang.String)"><!-- --></A><H3>
decode</H3>
<PRE>
public static byte[] <B>decode</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;s)</PRE>
<DL>
<DD>Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - the string to decode
<DT><B>Returns:</B><DD>the decoded data<DT><B>Since:</B></DT>
  <DD>1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="decode(java.lang.String, int)"><!-- --></A><H3>
decode</H3>
<PRE>
public static byte[] <B>decode</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;s,
                            int&nbsp;options)</PRE>
<DL>
<DD>Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - the string to decode<DD><CODE>options</CODE> - encode options such as URL_SAFE
<DT><B>Returns:</B><DD>the decoded data<DT><B>Since:</B></DT>
  <DD>1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="decodeToObject(java.lang.String)"><!-- --></A><H3>
decodeToObject</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</A> <B>decodeToObject</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;encodedObject)</PRE>
<DL>
<DD>Attempts to decode Base64 data and deserialize a Java
 Object within. Returns <tt>null</tt> if there was an error.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>encodedObject</CODE> - The Base64 data to decode
<DT><B>Returns:</B><DD>The decoded and deserialized object<DT><B>Since:</B></DT>
  <DD>1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeToFile(byte[], java.lang.String)"><!-- --></A><H3>
encodeToFile</H3>
<PRE>
public static boolean <B>encodeToFile</B>(byte[]&nbsp;dataToEncode,
                                   <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</PRE>
<DL>
<DD>Convenience method for encoding data to a file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dataToEncode</CODE> - byte array of data to encode in base64 form<DD><CODE>filename</CODE> - Filename for saving encoded data
<DT><B>Returns:</B><DD><tt>true</tt> if successful, <tt>false</tt> otherwise<DT><B>Since:</B></DT>
  <DD>2.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="decodeToFile(java.lang.String, java.lang.String)"><!-- --></A><H3>
decodeToFile</H3>
<PRE>
public static boolean <B>decodeToFile</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;dataToDecode,
                                   <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</PRE>
<DL>
<DD>Convenience method for decoding data to a file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dataToDecode</CODE> - Base64-encoded data as a string<DD><CODE>filename</CODE> - Filename for saving decoded data
<DT><B>Returns:</B><DD><tt>true</tt> if successful, <tt>false</tt> otherwise<DT><B>Since:</B></DT>
  <DD>2.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="decodeFromFile(java.lang.String)"><!-- --></A><H3>
decodeFromFile</H3>
<PRE>
public static byte[] <B>decodeFromFile</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</PRE>
<DL>
<DD>Convenience method for reading a base64-encoded
 file and decoding it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filename</CODE> - Filename for reading encoded data
<DT><B>Returns:</B><DD>decoded byte array or null if unsuccessful<DT><B>Since:</B></DT>
  <DD>2.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeFromFile(java.lang.String)"><!-- --></A><H3>
encodeFromFile</H3>
<PRE>
public static <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A> <B>encodeFromFile</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;filename)</PRE>
<DL>
<DD>Convenience method for reading a binary file
 and base64-encoding it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filename</CODE> - Filename for reading binary data
<DT><B>Returns:</B><DD>base64-encoded string or null if unsuccessful<DT><B>Since:</B></DT>
  <DD>2.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="encodeFileToFile(java.lang.String, java.lang.String)"><!-- --></A><H3>
encodeFileToFile</H3>
<PRE>
public static void <B>encodeFileToFile</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;infile,
                                    <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;outfile)</PRE>
<DL>
<DD>Reads <tt>infile</tt> and encodes it to <tt>outfile</tt>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>infile</CODE> - Input file<DD><CODE>outfile</CODE> - Output file<DT><B>Since:</B></DT>
  <DD>2.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="decodeFileToFile(java.lang.String, java.lang.String)"><!-- --></A><H3>
decodeFileToFile</H3>
<PRE>
public static void <B>decodeFileToFile</B>(<A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;infile,
                                    <A HREF="http://java.sun.com/j2se/1.3/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</A>&nbsp;outfile)</PRE>
<DL>
<DD>Reads <tt>infile</tt> and decodes it to <tt>outfile</tt>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>infile</CODE> - Input file<DD><CODE>outfile</CODE> - Output file<DT><B>Since:</B></DT>
  <DD>2.2</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Smack</b></EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/jivesoftware/smack/util/Base64.InputStream.html" title="class in org.jivesoftware.smack.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/jivesoftware/smack/util/Base64.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Base64.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>
<i>Copyright &copy; 2003-2007 Jive Software. </i>
</BODY>
</HTML>
