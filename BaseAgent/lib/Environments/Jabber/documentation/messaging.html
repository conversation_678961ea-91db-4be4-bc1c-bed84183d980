<html>
<head>
    <title>Smack: Chat - Jive Software</title>
    <link rel="stylesheet" type="text/css" href="style.css"/>
</head>

<body>

<div class="header">
    Messaging using Chats
</div>

<div class="nav">
    &laquo; <a href="index.html">Table of Contents</a>
</div>

<p>
    Sending messages back and forth is at the core of instant messaging. Although individual
    messages
    can be sent and received as packets, it's generally easier to treat the string of messages
    as a chat using the <tt>org.jivesoftware.smack.Chat</tt> class.
</p>

<p class="subheader">
    Chat
</p>

A chat creates a new thread of messages (using a thread ID) between two users. The
following code snippet demonstrates how to create a new Chat with a user and then send
them a text message:<p>

<div class="code"><pre><font color="gray"><i>// Assume we've created a Connection name "connection".</i></font>
ChatManager chatmanager = connection.getChatManager();
Chat newChat = chatmanager.createChat(<font
        color="green">"<EMAIL>"</font>, new MessageListener() {
    public void processMessage(Chat chat, Message message) {
        System.out.println(<font color="green">"Received message: "</font> + message);
    }
});

try {
    newChat.sendMessage(<font color="green">"Howdy!"</font>);
}
catch (XMPPException e) {
    System.out.println(<font color="green">"Error Delivering block"</font>);
}
</pre>
</div>
<p>

    The <tt>Chat.sendMessage(String)</tt> method is a convenience method that creates a Message
    object, sets the body using the String parameter, then sends the message. In the case
    that you wish to set additional values on a Message before sending it, use the
    <tt>Chat.createMessage()</tt> and <tt>Chat.sendMessage(Message)</tt> methods, as in the
    following code snippet:

<p>

<div class="code"><pre>
Message newMessage = new Message();
newMessage.setBody(<font color="green">"Howdy!"</font>);
message.setProperty(<font color="green">"favoriteColor"</font>, <font color="green">"red"</font>);
newChat.sendMessage(newMessage);
</pre>
</div>
<p>

    You'll also notice in the example above that we specified a MessageListener when creating a
    chat.
    The listener is notified any time a new message arrives from the other user in the chat.
    The following code snippet uses the listener as a parrot-bot -- it echoes back everything the
    other user types.

<p>

<div class="code"><pre>
<font color="gray"><i> // Assume a MessageListener we've setup with a chat.</i></font>

    public void processMessage(Chat chat, Message message) {
        <font color="gray"><i>// Send back the same text the other user sent us.</i></font>
        chat.sendMessage(message.getBody());
    }
</pre>
</div>

<p class="subheader">
    Incoming Chat
</p>

When chats are prompted by another user, the setup is slightly different since
you are receiving a chat message first.  Instead of explicitly creating a chat to send
messages, you need to register to handle newly created Chat instances when the ChatManager 
creates them.  
</br>
</br>
The ChatManager will already find a matching chat (by thread id) and if none exists, then it
will create a new one that does match.  To get this new chat, you have to register to be 
notified when it happens.  You can register a message listener to receive all future messages as
part of this handler.<p>

<div class="code"><pre><font color="gray"><i>// Assume we've created a Connection name "connection".</i></font>
ChatManager chatmanager = connection.getChatManager().addChatListener(
    new ChatManagerListener() {
        @Override
        public void chatCreated(Chat chat, boolean createdLocally)
        {
            if (!createdLocally)
                chat.addMessageListener(new MyNewMessageListener());;
        }
    });
</pre>
</div>
In addition to thread based chat messages, there are some clients that
do not send a thread id as part of the chat.  To handle this scenario,
Smack will attempt match the incoming messages to the best fit existing
chat, based on the JID.  It will attempt to find a chat with the same full 
JID, failing that, it will try the base JID.  If no existing chat to the 
user can found, then a new one is created.
<p>

    <br clear="all"/><br><br>

<div class="footer">
    Copyright &copy; Jive Software 2002-2008
</div>

</body>
</html>
