<html>
<head>
	<title>Smack: Debugging - Jive Software</title>
	<link rel="stylesheet" type="text/css" href="style.css" />
</head>

<body>

<div class="header">
Debugging with Smack
</div>

<div class="nav">
&laquo; <a href="index.html">Table of Contents</a>
</div>

<p>
Smack includes two built-in debugging consoles that will let you track all XML traffic between 
the client and server. A <a href="#lite">lite debugger</a> which is part of the <tt>smack.jar</tt> 
and an <a href="#enhanced">enhanced debugger</a> contained in <tt>smackx-debug.jar</tt>.
</p>

<p>
Debugging mode can be enabled in two different ways:
</p>

<ol>
	<li>Add the following line of code <b>before</b> creating new connections:<p> 
		<tt>Connection.DEBUG_ENABLED = true;</tt><p>

	<li>Set the Java system property <tt>smack.debugEnabled</tt> to true. The 
		system property can be set on the command line such as:<p> 
		<tt>java -Dsmack.debugEnabled=true SomeApp </tt>
</ol>

<p>
If you wish to explicitly disable debug mode in your application, including using the command-line parameter, 
add the following line to your application before opening new connections:
</p>

<p>
<tt>Connection.DEBUG_ENABLED = false;</tt>
</p>

<p>
Smack uses the following logic to decide the debugger console to use:
</p>

<ol>
	<li>It will first try use the debugger class specified in the Java system property 
	<tt>smack.debuggerClass</tt>. If you need to develop your own debugger, 
	implement the <tt>SmackDebugger</tt> interface and then set the system property 
	on the command line such as:<p> 
		<tt>java -Dsmack.debuggerClass=my.company.com.MyDebugger SomeApp </tt><p>

	<li>If step 1 fails then Smack will try to use the enhanced debugger. The
	file <tt>smackx-debug.jar</tt> contains the enhanced debugger. Therefore you will need
	to place the jar file in the classpath. For situations where space is an issue you
	may want to only deploy <tt>smack.jar</tt> in which case the enhanced debugger won't be
	available.<p> 

	<li>The last option if the previous two steps fail is to use the lite debugger. The lite 
	debugger is a very good option for situations where you need to have low memory footprint.
</ol>

<p class="subheader">
<a name="enhanced">Enhanced Debugger</a>
</p>

<img src="images/enhanceddebugger.png" width="479" height="400" alt="Full Debug Window" border="0" align="right">

When debugging mode is enabled, a debug window will appear containing tabs for each new created connection.
The window will contain the following information:

<ul>
    <li>Connection tabs -- each tab shows debugging information related to the connection.
	<li>Smack info tab -- shows information about Smack (e.g. Smack version, installed components, etc.).
</ul>

The connection tab will contain the following information:
<ul>
    <li>All Packets -- shows sent and received packets information parsed by Smack.
	<li>Raw Sent Packets -- raw XML traffic generated by Smack and sent to the server.
    <li>Raw Received Packets -- raw XML traffic sent by the server to the client.
    <li>Ad-hoc message -- allows to send ad-hoc packets of any type.
    <li>Information -- shows connection state and statistics.
</ul>

<br clear="right">

<p class="subheader">
<a name="lite">Lite Debugger</a>
</p>

<img src="images/debugwindow.gif" width="359" height="399" alt="Lite Debug Window" border="0" align="right">

When debugging mode is enabled, a debug window will appear when each new connection is created.
The window will contain the following information:

<ul>
	<li>Client Traffic (red text) -- raw XML traffic generated by Smack and sent to the server.
    <li>Server Traffic (blue text) -- raw XML traffic sent by the server to the client.
    <li>Interpreted Packets (green text) -- shows XML packets from the server as parsed by Smack.
</ul>

Right click on any of the panes to bring up a menu with the choices to copy of the contents 
to the system clipboard or to clear the contents of the pane.

<br clear="all" /><br><br>

<div class="footer">
Copyright &copy; Jive Software 2002-2008
</div>

</body>
</html>
