<html>
<head>
    <title>Smack: Connection Management - Jive Software</title>
    <link rel="stylesheet" type="text/css" href="style.css"/>
</head>

<body>

<div class="header">
    Smack: Connection Management
</div>

<div class="nav">
    &laquo; <a href="index.html">Table of Contents</a>
</div>

<p class="subheader">
    Creating a Connection
</p>

<p>
    The <tt>org.jivesoftware.smack.Connection</tt> class manages your connection to an XMPP
    server. The default implementation is the <tt>org.jivesoftware.smack.XMPPConnection</tt>
    class.  Two constructors are mainly used. The first, <tt>XMPPConnection(String)</tt> takes
    the server name you'd like to connect to as an argument. All default connection settings will
    be used:
    <ul>
        <li>A DNS SRV lookup will be performed to find the exact address and port (typically 5222)
        that the server resides at.</li>
        <li>The maximum security possible will be negotiated with the server, including TLS encryption,
            but the connection will fall back to lower security settings if necessary.
        </li>
        <li>The XMPP resource name "Smack" will be used for the connection.</li>
    </ul>

    Alternatively, you can use the <tt>XMPPServer(ConnectionConfiguration)</tt> constructor to
    specify advanced connection settings. Some of these settings include:

    <ul>
        <li>Manually specify the server address and port of the server rather than using a DNS SRV
        lookup.</li>
        <li>Enable connection compression.</li>
        <li>Customize security settings, such as flagging the connection to require TLS encryption
        in order to connect.</li>
        <li>Specify a custom connection resource name such as "Work" or "Home". Every connection
        by a user to a server must have a unique resource name. For the user "<EMAIL>", the
        full address with resource might be "<EMAIL>/Smack". With unique resource names, a user
        can be logged into the server from multiple locations at once, or using multiple devices. The
        presence priority value used with each resource will determine which particular connection
        receives messages to the bare address ("<EMAIL>" in our example).</li>
    </ul>

</p>

<p class="subheader">
    Connect and Disconnect
</p>

<div class="code">
<pre>
<font color="gray"><i>// Create the configuration for this new connection</i></font>
ConnectionConfiguration config = new ConnectionConfiguration(<font color="green">"jabber.org"</font>, 5222);
config.setCompressionEnabled(true);
config.setSASLAuthenticationEnabled(true);

Connection connection = new XMPPConnection(config);
<font color="gray"><i>// Connect to the server</i></font>
connection.connect();
<font color="gray"><i>// Log into the server</i></font>
connection.login(<font color="green">"username"</font>, <font color="green">"password"</font>, <font color="green">"SomeResource"</font>);
....
<font color="gray"><i>// Disconnect from the server</i></font>
connection.disconnect();
</pre></div>

<p>
By default Smack will try to reconnect the connection in case it was abruptly disconnected. Use
<i>ConnectionConfiguration#setReconnectionAllowed(boolean) to turn on/off this feature. The reconnection
manager will try to immediately reconnect to the server and increase the delay between attempts as
successive reconnections keep failing.</i>
<br>
In case you want to force a reconnection while the reconnetion manager is waiting for the next
reconnection, you can just use <i>Connection#connect()</i> and a new attempt will be made.
If the manual attempt also failed then the reconnection manager will still continue the
reconnection job.
</p>

    <br clear="all"/><br><br>

<div class="footer">
    Copyright &copy; Jive Software 2002-2008
</div>

</body>
</html>
