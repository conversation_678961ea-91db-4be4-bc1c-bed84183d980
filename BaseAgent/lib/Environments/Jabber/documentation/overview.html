<html>
<head>
	<title>Smack: Overview - Jive Software</title>
	<link rel="stylesheet" type="text/css" href="style.css" />
</head>

<body>

<div class="header">
Smack Overview
</div>

<div class="nav">
&laquo; <a href="index.html">Table of Contents</a>
</div>

<p>

Smack is a library for communicating with XMPP servers to perform real-time communications, including
instant messaging and group chat.<p>

<p class="subheader">
Smack Key Advantages
</p>

<ul>
	<li>Extremely simple to use, yet powerful API. Sending a text message to a user
	    can be accomplished in only a few lines of code:

<div class="code"><pre>
Connection connection = <font color="navy"><b>new</b></font> XMPPConnection(<font color="green">"jabber.org"</font>);
connection.connect();
connection.login(<font color="green">"mtucker"</font>, <font color="green">"password"</font>);
Chat chat = connection.getChatManager().createChat(<font color="green">"<EMAIL>"</font>, new MessageListener() {

    public void processMessage(Chat chat, Message message) {
        System.out.println(<font color="green">"Received message: "</font> + message);
    }
});
chat.sendMessage(<font color="green">"Howdy!"</font>);
</pre></div>


	<li>Doesn't force you to code at the packet level, as other libraries do. Smack provides 
		intelligent higher level constructs such as the <tt>Chat</tt> and <tt>Roster</tt> 
		classes, which let you program more efficiently.

	<li>Does not require that you're familiar with the XMPP XML format, or even that you're familiar with XML.

	<li>Provides easy machine to machine communication. Smack lets you set any number of properties on
		each message, including properties that are Java objects.

	<li>Open Source under the Apache License, which means you can incorporate Smack into your commercial or
		non-commercial applications.
</ul>

<p class="subheader">
About XMPP
</p>

XMPP (eXtensible Messaging and Presence Protocol) is an open protocol standardized by the IETF
and supported and extended by the XMPP Standards Foundation
((<a href="http://www.jabber.org">http://www.xmpp.org</a>).
 
<p class="subheader">
How To Use This Documentation
</p>

This documentation assumes that you're already familiar with the main features of XMPP
instant messaging. It's also highly recommended that you open the Javadoc API guide and
use that as a reference while reading through this documentation.
 
<br clear="all" /><br><br>
<div class="footer">
Copyright &copy; Jive Software 2002-2008
</div>

</body>
</html>
