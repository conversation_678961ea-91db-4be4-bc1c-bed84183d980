<html>
<head>
	<title>Smack: Getting Started - Jive Software</title>
	<link rel="stylesheet" type="text/css" href="style.css" />
</head>

<body>

<div class="header">
Smack: Getting Started
</div>

<div class="nav">
&laquo; <a href="index.html">Table of Contents</a>
</div>

<p>
This document will introduce you to the Smack API and provide an overview of 
important classes and concepts.
</p>

<p class="subheader">
JAR Files and Requirements
</p>

Smack is meant to be easily embedded into any existing JDK 1.5 or later Java application.
It has no external dependencies (except for the Jingle voice chat functionality) and is optimized
to be as small as possible. The library ships as several JAR files to provide more flexibility
over which features applications require:

<ul>
    <li><tt>smack.jar</tt> -- provides core XMPP functionality and is the only <b>required</b>
    library. All XMPP features that are part of the XMPP RFCs are included.</li>
    <li><tt>smackx.jar</tt> -- support for many of the the extensions (XEPs) defined
    by the XMPP Standards Foundation, including multi-user chat, file transfer, user search, etc.
    The extensions are documented in the <a href="extensions/index.html">extensions manual</a>.</li>
    <li><tt>smackx-debug.jar</tt> -- an enhanced GUI debugger for protocol traffic. It will
    automatically be used when found in the classpath and when <a href="debugging.html">debugging</a>
    is enabled.</li>
</ul>


<p class="subheader">
Establishing a Connection
</p>

The <tt>XMPPConnection</tt> class is used to create a connection to an 
XMPP server. Below are code examples for making a connection:<p>

<div class="code">
<pre>
<font color="gray"><i>// Create a connection to the jabber.org server.</i></font>
Connection conn1 = <font color="navy"><b>new</b></font> XMPPConnection(<font color="green">"jabber.org"</font>);
conn1.connect();

<font color="gray"><i>// Create a connection to the jabber.org server on a specific port.</i></font>
ConnectionConfiguration config = new ConnectionConfiguration(<font color="green">"jabber.org"</font>, 5222);
Connection conn2 = <font color="navy"><b>new</b></font> XMPPConnection(config);
conn2.connect();
</pre></div>

<p>Note that maximum security will be used when connecting to the server by default (and when possible),
    including use of TLS encryption. The ConnectionConfiguration class provides advanced control
    over the connection created, such as the ability to disable or require encryption. See
    <a href="connections.html">Connection Management</a> for full details.</p>

<p>Once you've created a connection, you should login using a username and password
with the <tt>Connection.login(String username, String password)</tt> method.
Once you've logged in, you can being chatting with other users by creating
new <tt>Chat</tt> or <tt>GroupChat</tt> objects.

<p class="subheader">
Working with the Roster
</p>
The roster lets you keep track of the availability (presence) of other users. Users
can be organized into groups such as "Friends" and "Co-workers", and then you
discover whether each user is online or offline.<p> 

Retrieve the roster using the <tt>Connection.getRoster()</tt> method. The roster
class allows you to find all the roster entries, the groups they belong to, and the
current presence status of each entry.

<p class="subheader">
Reading and Writing Packets
</p>

Each message to the XMPP server from a client is called a packet and is
sent as XML. The <tt>org.jivesoftware.smack.packet</tt> package contains
classes that encapsulate the three different basic packet types allowed by 
XMPP (message, presence, and IQ). Classes such as <tt>Chat</tt> and <tt>GroupChat</tt>
provide higher-level constructs that manage creating and sending packets
automatically, but you can also create and send packets directly. Below
is a code example for changing your presence to let people know you're unavailable
and "out fishing":<p>

<div class="code">
<pre>
<font color="gray"><i>// Create a new presence. Pass in false to indicate we're unavailable.</i></font>
Presence presence = new Presence(Presence.Type.unavailable);
presence.setStatus(<font color="green">"Gone fishing"</font>);
<font color="gray"><i>// Send the packet (assume we have a Connection instance called "con").</i></font>
con.sendPacket(presence);
</pre></div>
<p>

Smack provides two ways to read incoming packets: <tt>PacketListener</tt>, and 
<tt>PacketCollector</tt>. Both use <tt>PacketFilter</tt> instances to determine 
which packets should be processed. A packet listener is used for event style programming,
while a packet collector has a result queue of packets that you can do
polling and blocking operations on. So, a packet listener is useful when
you want to take some action whenever a packet happens to come in, while a
packet collector is useful when you want to wait for a specific packet
to arrive. Packet collectors and listeners can be created using an
Connection instance.


<p><div class="footer">
Copyright &copy; Jive Software 2002-2008
</div>

</body>
</html>